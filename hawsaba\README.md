# حوسبة - نظام إدارة الأعمال

نظام شامل لإدارة الأعمال والمبيعات والمشتريات والمخازن باللغة العربية، مطور باستخدام HTML5, CSS3, JavaScript وBootstrap.

## المميزات الرئيسية

### 📊 لوحة التحكم
- عرض إحصائيات شاملة للمبيعات والمشتريات
- متابعة رصيد الخزنة والأرصدة الافتتاحية
- عرض الأصناف منخفضة المخزون
- آخر الفواتير والمعاملات المالية

### 👥 إدارة العملاء والموردين
- إضافة وتعديل وحذف العملاء مع الأرصدة الافتتاحية
- إدارة بيانات الموردين والأرصدة الدائنة
- تتبع أرصدة العملاء والموردين
- إدارة حدود الائتمان والمستحقات

### 👨‍💼 إدارة المناديب
- إضافة وإدارة بيانات المناديب مع الأرصدة الافتتاحية
- تحديد نسب العمولة لكل مندوب
- ربط المناديب بالمناطق الجغرافية
- تتبع أداء المناديب ومبيعاتهم

### 🏪 إدارة المخازن والأصناف
- إدارة متعددة المخازن مع تتبع المخزون
- إضافة وتصنيف الأصناف بأسعار متعددة
- تتبع المخزون في الوقت الفعلي
- تحديد الحد الأدنى والأقصى للمخزون
- تنبيهات المخزون المنخفض

### 🧾 نظام الفواتير المتقدم
#### فواتير البيع
- فواتير نقدية فورية
- فواتير آجلة بتواريخ استحقاق
- فواتير تقسيط بجدولة المدفوعات
- عروض الأسعار القابلة للتحويل لفواتير

#### فواتير الشراء
- إدارة فواتير الشراء من الموردين
- ربط الفواتير بالموردين والمخازن
- تحديث المخزون تلقائياً عند الاستلام

### 💰 إدارة الخزنة والسندات
- تتبع رصيد الخزنة مع الرصيد الافتتاحي
- سندات القبض من العملاء
- سندات الدفع للموردين والمصروفات
- تقارير الحركات المالية التفصيلية
- ملخص مالي شامل يومي وشهري

### 🔄 مرتجعات المبيعات والمشتريات
- إدارة مرتجعات المبيعات من العملاء
- معالجة مرتجعات المشتريات للموردين
- تحديث المخزون والحسابات تلقائياً
- ربط المرتجعات بالفواتير الأصلية

### 📈 التقارير المتقدمة
- تقارير المبيعات التفصيلية والإجمالية
- تقارير المشتريات والموردين
- تقارير المخزون والحركة
- تقارير الأرباح والخسائر
- الأصناف الأكثر مبيعاً والأقل حركة
- تقارير أداء المناديب

### 👤 إدارة المستخدمين والصلاحيات
- نظام تسجيل دخول آمن
- إدارة صلاحيات المستخدمين المتقدمة
- أدوار مختلفة (مدير، مبيعات، مخازن، محاسب)
- تتبع نشاط المستخدمين

### ⚙️ الإعدادات المتقدمة
- إعدادات الشركة والبيانات الأساسية
- إعدادات الأسعار المتعددة (جملة، نصف جملة، تجزئة)
- إعدادات الضرائب والعملة
- إعدادات التنبيهات والإشعارات
- إدارة الأرصدة الافتتاحية

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **UI Framework**: Bootstrap 5
- **Icons**: Font Awesome 6
- **Fonts**: Cairo (Google Fonts)
- **Storage**: LocalStorage (قاعدة بيانات محلية)
- **PDF Generation**: jsPDF مع AutoTable

## كيفية التشغيل

1. قم بتحميل ملفات المشروع
2. افتح ملف `index.html` في المتصفح
3. استخدم البيانات الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## تحميل البيانات التجريبية

يمكنك تحميل بيانات تجريبية للاختبار من خلال:
1. الضغط على زر "تحميل بيانات تجريبية" في شريط التنقل
2. أو استدعاء الدالة `loadSampleData()` من وحدة التحكم

## هيكل المشروع

```
hawsaba/
├── index.html              # الصفحة الرئيسية
├── css/
│   └── style.css          # ملف التصميم الرئيسي
├── js/
│   ├── app.js             # التطبيق الرئيسي
│   ├── database.js        # إدارة قاعدة البيانات
│   └── sample-data.js     # البيانات التجريبية
└── README.md              # ملف التوثيق
```

## الوظائف المتاحة

### العملاء
- إضافة عميل جديد مع رصيد افتتاحي
- تعديل بيانات العميل والحد الائتماني
- حذف العميل مع التحقق من المعاملات
- عرض رصيد العميل الحالي والحركات

### الموردين
- إضافة مورد جديد مع رصيد افتتاحي
- تعديل بيانات المورد والرقم الضريبي
- إدارة شروط الدفع والائتمان
- تتبع المستحقات والمدفوعات

### الأصناف
- إضافة صنف جديد مع أسعار متعددة
- تصنيف الأصناف حسب الفئات
- تحديد أسعار الشراء والبيع المختلفة
- إدارة وحدات القياس والباركود

### المخزون
- تتبع الكميات في كل مخزن
- تحديثات تلقائية عند البيع/الشراء
- تنبيهات المخزون المنخفض
- جرد المخزون الدوري

### الفواتير
- إنشاء فواتير بأنواع مختلفة
- طباعة الفواتير بتصميم احترافي
- تتبع حالة الدفع والاستحقاق
- ربط الفواتير بالعملاء/الموردين

## المتطلبات

- متصفح حديث يدعم HTML5 و JavaScript ES6+
- اتصال بالإنترنت لتحميل Bootstrap و Font Awesome
- دقة شاشة لا تقل عن 1024x768 للاستخدام الأمثل

## المميزات الخاصة

### الأرصدة الافتتاحية
- إدخال أرصدة افتتاحية للعملاء (مدينة)
- إدخال أرصدة افتتاحية للموردين والمناديب (دائنة)
- تتبع الأرصدة وتحديثها مع المعاملات

### الأسعار المتعددة
- سعر الجملة للكميات الكبيرة
- سعر نصف الجملة للكميات المتوسطة
- سعر التجزئة للكميات الصغيرة
- إمكانية تفعيل/إلغاء مستويات الأسعار

### طباعة PDF
- طباعة جميع التقارير بصيغة PDF
- تصميم احترافي للفواتير والتقارير
- إمكانية حفظ وإرسال التقارير

## الدعم والتطوير

هذا النظام قابل للتطوير والتخصيص حسب احتياجات العمل. يمكن إضافة المزيد من الوظائف مثل:

- تصدير البيانات إلى Excel
- نظام النسخ الاحتياطي التلقائي
- إشعارات الويب للتنبيهات
- تكامل مع أنظمة الدفع الإلكتروني
- واجهة برمجة تطبيقات (API) للتكامل الخارجي

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة**: هذا النظام يستخدم LocalStorage لحفظ البيانات، مما يعني أن البيانات محفوظة محلياً في المتصفح. لاستخدام النظام في بيئة إنتاج، يُنصح بتطوير backend مع قاعدة بيانات حقيقية مثل MySQL أو PostgreSQL.

## معلومات الاتصال

للدعم الفني والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.hawsaba.com

**حوسبة - نظام إدارة الأعمال الشامل**
