// حوسبة - نظام إدارة الأعمال - البيانات التجريبية
function loadSampleData() {
    const db = window.dbManager.getDatabase();
    
    // Sample customers with opening balances
    const sampleCustomers = [
        {
            id: 1,
            name: 'أحمد محمد علي',
            phone: '01012345678',
            email: '<EMAIL>',
            address: 'شارع النيل، المعادي، القاهرة',
            taxNumber: '*********',
            creditLimit: 50000,
            openingBalance: 15000,
            balanceType: 'debit', // مدين
            isActive: true,
            notes: 'عميل مميز'
        },
        {
            id: 2,
            name: 'فاطمة أحمد حسن',
            phone: '01098765432',
            email: '<EMAIL>',
            address: 'شارع الجمهورية، وسط البلد، القاهرة',
            taxNumber: '*********',
            creditLimit: 30000,
            openingBalance: 8500,
            balanceType: 'debit',
            isActive: true,
            notes: 'عميل منتظم'
        },
        {
            id: 3,
            name: 'محمد عبد الرحمن',
            phone: '01*********',
            email: '<EMAIL>',
            address: 'شارع الهرم، الجيزة',
            taxNumber: '*********',
            creditLimit: 75000,
            openingBalance: 25000,
            balanceType: 'debit',
            isActive: true,
            notes: 'عميل كبير'
        }
    ];

    // Sample suppliers with opening balances
    const sampleSuppliers = [
        {
            id: 1,
            name: 'شركة النور للتجارة',
            contactPerson: 'خالد النور',
            phone: '0*********0',
            email: '<EMAIL>',
            address: 'المنطقة الصناعية، العاشر من رمضان',
            taxNumber: '*********',
            openingBalance: 12000,
            balanceType: 'credit', // دائن
            isActive: true,
            notes: 'مورد رئيسي'
        },
        {
            id: 2,
            name: 'مؤسسة الأمل التجارية',
            contactPerson: 'سعد الأمل',
            phone: '01*********',
            email: '<EMAIL>',
            address: 'شارع الصناعة، الإسكندرية',
            taxNumber: '*********',
            openingBalance: 8500,
            balanceType: 'credit',
            isActive: true,
            notes: 'مورد موثوق'
        }
    ];

    // Sample representatives with opening balances
    const sampleRepresentatives = [
        {
            id: 1,
            name: 'عمر السيد',
            phone: '01111111111',
            email: '<EMAIL>',
            address: 'شارع الجامعة، الجيزة',
            commissionRate: 5,
            area: 'القاهرة الكبرى',
            openingBalance: 2500,
            balanceType: 'credit',
            isActive: true,
            notes: 'مندوب مبيعات أول'
        },
        {
            id: 2,
            name: 'نورا أحمد',
            phone: '01222222222',
            email: '<EMAIL>',
            address: 'شارع الكورنيش، الإسكندرية',
            commissionRate: 4,
            area: 'الإسكندرية',
            openingBalance: 1800,
            balanceType: 'credit',
            isActive: true,
            notes: 'مندوبة مبيعات'
        }
    ];

    // Sample products
    const sampleProducts = [
        {
            id: 1,
            name: 'لابتوب ديل إنسبايرون',
            code: 'DELL-INS-001',
            barcode: '*********0123',
            categoryId: 1,
            unitId: 1,
            purchasePrice: 15000,
            wholesalePrice: 18000,
            semiWholesalePrice: 19000,
            retailPrice: 20000,
            minStock: 5,
            maxStock: 50,
            description: 'لابتوب ديل إنسبايرون 15 بوصة',
            isActive: true
        },
        {
            id: 2,
            name: 'ماوس لاسلكي',
            code: 'MOUSE-WL-001',
            barcode: '2345678901234',
            categoryId: 1,
            unitId: 1,
            purchasePrice: 150,
            wholesalePrice: 200,
            semiWholesalePrice: 220,
            retailPrice: 250,
            minStock: 20,
            maxStock: 200,
            description: 'ماوس لاسلكي عالي الجودة',
            isActive: true
        },
        {
            id: 3,
            name: 'كيبورد ميكانيكي',
            code: 'KB-MECH-001',
            barcode: '3456789012345',
            categoryId: 1,
            unitId: 1,
            purchasePrice: 800,
            wholesalePrice: 1000,
            semiWholesalePrice: 1100,
            retailPrice: 1200,
            minStock: 10,
            maxStock: 100,
            description: 'كيبورد ميكانيكي للألعاب',
            isActive: true
        }
    ];

    // Sample stock for warehouses
    const sampleStock = [
        { productId: 1, warehouseId: 1, quantity: 25, reservedQuantity: 0 },
        { productId: 2, warehouseId: 1, quantity: 150, reservedQuantity: 0 },
        { productId: 3, warehouseId: 1, quantity: 45, reservedQuantity: 0 }
    ];

    // Add sample data to database
    db.customers = [...db.customers, ...sampleCustomers];
    db.suppliers = [...db.suppliers, ...sampleSuppliers];
    db.representatives = [...db.representatives, ...sampleRepresentatives];
    db.products = [...db.products, ...sampleProducts];
    
    // Initialize stock if not exists
    if (!db.stock) {
        db.stock = [];
    }
    db.stock = [...db.stock, ...sampleStock];

    // Add sample sales invoice
    const sampleSalesInvoice = {
        id: 1,
        invoiceNumber: 'INV-001',
        customerId: 1,
        representativeId: 1,
        warehouseId: 1,
        invoiceDate: new Date().toISOString(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        paymentType: 'credit', // نقدي، آجل، تقسيط
        status: 'pending', // pending, paid, overdue
        items: [
            {
                productId: 1,
                quantity: 2,
                unitPrice: 20000,
                discount: 0,
                total: 40000
            },
            {
                productId: 2,
                quantity: 5,
                unitPrice: 250,
                discount: 50,
                total: 1200
            }
        ],
        subtotal: 41200,
        discountAmount: 0,
        taxAmount: 5768,
        totalAmount: 46968,
        paidAmount: 0,
        remainingAmount: 46968,
        notes: 'فاتورة تجريبية'
    };

    db.salesInvoices = [...db.salesInvoices, sampleSalesInvoice];

    // Add sample purchase invoice
    const samplePurchaseInvoice = {
        id: 1,
        invoiceNumber: 'PI-001',
        supplierId: 1,
        warehouseId: 1,
        invoiceDate: new Date().toISOString(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'pending',
        items: [
            {
                productId: 1,
                quantity: 10,
                unitPrice: 15000,
                discount: 0,
                total: 150000
            },
            {
                productId: 2,
                quantity: 50,
                unitPrice: 150,
                discount: 500,
                total: 7000
            }
        ],
        subtotal: 157000,
        discountAmount: 0,
        taxAmount: 21980,
        totalAmount: 178980,
        paidAmount: 0,
        remainingAmount: 178980,
        notes: 'فاتورة شراء تجريبية'
    };

    db.purchaseInvoices = [...db.purchaseInvoices, samplePurchaseInvoice];

    // Update treasury with opening balance
    db.treasury.openingBalance = 100000;
    db.treasury.balance = 100000;
    db.treasury.transactions.push({
        id: 1,
        type: 'opening_balance',
        amount: 100000,
        description: 'رصيد افتتاحي',
        date: new Date().toISOString(),
        reference: 'OB-001'
    });

    // Save updated database
    window.dbManager.saveDatabase(db);
    
    // Show success message
    if (typeof showAlert === 'function') {
        showAlert('تم تحميل البيانات التجريبية بنجاح!', 'success');
    } else {
        alert('تم تحميل البيانات التجريبية بنجاح!');
    }
    
    // Reload current page if app exists
    if (window.app && typeof window.app.loadPage === 'function') {
        window.app.loadPage(window.app.currentPage);
    }
}

// Function to clear all data and reload sample data
function resetToSampleData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات وإعادة تحميل البيانات التجريبية؟')) {
        window.dbManager.clearDatabase();
        loadSampleData();
    }
}

// Export functions for global use
window.loadSampleData = loadSampleData;
window.resetToSampleData = resetToSampleData;
