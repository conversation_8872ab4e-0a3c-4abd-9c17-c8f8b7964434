// حوسبة - نظام إدارة الأعمال - إدارة قاعدة البيانات
class DatabaseManager {
    constructor() {
        this.dbName = 'hawsabaDB';
        this.init();
    }

    init() {
        // Initialize database if not exists
        if (!localStorage.getItem(this.dbName)) {
            this.createInitialDatabase();
        }
    }

    createInitialDatabase() {
        const initialDB = {
            customers: [],
            suppliers: [],
            representatives: [],
            warehouses: [
                {
                    id: 1,
                    name: 'المخزن الرئيسي',
                    location: 'المقر الرئيسي',
                    manager: 'مدير المخزن',
                    phone: '01234567890',
                    isActive: true,
                    createdAt: new Date().toISOString()
                }
            ],
            products: [],
            salesInvoices: [],
            purchaseInvoices: [],
            quotations: [],
            salesReturns: [],
            purchaseReturns: [],
            receiptVouchers: [],
            paymentVouchers: [],
            treasury: { 
                balance: 0, 
                transactions: [],
                openingBalance: 0,
                openingDate: new Date().toISOString()
            },
            users: [
                {
                    id: 1,
                    username: 'admin',
                    password: 'admin123',
                    name: 'المدير العام',
                    role: 'admin',
                    permissions: ['all'],
                    isActive: true,
                    createdAt: new Date().toISOString()
                }
            ],
            settings: {
                companyName: 'حوسبة - نظام إدارة الأعمال',
                companyAddress: 'العنوان الرئيسي للشركة',
                companyPhone: '01234567890',
                companyEmail: '<EMAIL>',
                companyWebsite: 'www.hawsaba.com',
                taxRate: 14,
                currency: 'EGP',
                currencySymbol: 'ج.م',
                invoicePrefix: 'INV-',
                purchaseInvoicePrefix: 'PI-',
                quotationPrefix: 'QUO-',
                receiptVoucherPrefix: 'RV-',
                paymentVoucherPrefix: 'PV-',
                purchaseOrderPrefix: 'PO-',
                salesReturnPrefix: 'SR-',
                purchaseReturnPrefix: 'PR-',
                // Price settings
                enableMultiplePrices: true,
                enableWholesalePrice: true,
                enableSemiWholesalePrice: true,
                enableRetailPrice: true,
                defaultWholesaleMinQty: 100,
                defaultSemiWholesaleMinQty: 50,
                defaultRetailMaxQty: 10,
                // Pricing labels
                wholesalePriceLabel: 'سعر الجملة',
                semiWholesalePriceLabel: 'سعر نصف الجملة',
                retailPriceLabel: 'سعر التجزئة',
                // Other settings
                enableLowStockAlerts: true,
                lowStockThreshold: 10,
                enableAutoBackup: false,
                backupFrequency: 'daily',
                enableNotifications: true,
                dateFormat: 'DD/MM/YYYY',
                timeFormat: '12',
                language: 'ar',
                theme: 'light',
                // Opening balances
                enableOpeningBalances: true,
                openingBalanceDate: new Date().toISOString(),
                // Financial year
                financialYearStart: '01/01',
                financialYearEnd: '31/12'
            },
            categories: [
                {
                    id: 1,
                    name: 'عام',
                    description: 'تصنيف عام للمنتجات',
                    isActive: true,
                    createdAt: new Date().toISOString()
                }
            ],
            units: [
                {
                    id: 1,
                    name: 'قطعة',
                    symbol: 'قطعة',
                    isActive: true,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'كيلو',
                    symbol: 'كجم',
                    isActive: true,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 3,
                    name: 'متر',
                    symbol: 'م',
                    isActive: true,
                    createdAt: new Date().toISOString()
                }
            ]
        };
        
        localStorage.setItem(this.dbName, JSON.stringify(initialDB));
    }

    getDatabase() {
        const db = localStorage.getItem(this.dbName);
        return db ? JSON.parse(db) : null;
    }

    saveDatabase(db) {
        localStorage.setItem(this.dbName, JSON.stringify(db));
    }

    // Generic CRUD operations
    create(table, data) {
        const db = this.getDatabase();
        if (!db[table]) {
            db[table] = [];
        }
        
        // Generate ID if not provided
        if (!data.id) {
            const maxId = db[table].length > 0 ? Math.max(...db[table].map(item => item.id || 0)) : 0;
            data.id = maxId + 1;
        }
        
        // Add timestamps
        data.createdAt = new Date().toISOString();
        data.updatedAt = new Date().toISOString();
        
        db[table].push(data);
        this.saveDatabase(db);
        return data;
    }

    read(table, id = null) {
        const db = this.getDatabase();
        if (!db[table]) {
            return id ? null : [];
        }
        
        if (id) {
            return db[table].find(item => item.id === id) || null;
        }
        
        return db[table];
    }

    update(table, id, data) {
        const db = this.getDatabase();
        if (!db[table]) {
            return null;
        }
        
        const index = db[table].findIndex(item => item.id === id);
        if (index === -1) {
            return null;
        }
        
        // Preserve original timestamps and add update timestamp
        data.createdAt = db[table][index].createdAt;
        data.updatedAt = new Date().toISOString();
        
        db[table][index] = { ...db[table][index], ...data };
        this.saveDatabase(db);
        return db[table][index];
    }

    delete(table, id) {
        const db = this.getDatabase();
        if (!db[table]) {
            return false;
        }
        
        const index = db[table].findIndex(item => item.id === id);
        if (index === -1) {
            return false;
        }
        
        db[table].splice(index, 1);
        this.saveDatabase(db);
        return true;
    }

    // Search functionality
    search(table, query, fields = []) {
        const db = this.getDatabase();
        if (!db[table]) {
            return [];
        }
        
        if (!query) {
            return db[table];
        }
        
        const searchTerm = query.toLowerCase();
        
        return db[table].filter(item => {
            if (fields.length === 0) {
                // Search in all string fields
                return Object.values(item).some(value => 
                    typeof value === 'string' && value.toLowerCase().includes(searchTerm)
                );
            } else {
                // Search in specific fields
                return fields.some(field => 
                    item[field] && typeof item[field] === 'string' && 
                    item[field].toLowerCase().includes(searchTerm)
                );
            }
        });
    }

    // Backup and restore
    exportData() {
        const db = this.getDatabase();
        const dataStr = JSON.stringify(db, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `hawsaba-backup-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    localStorage.setItem(this.dbName, JSON.stringify(data));
                    resolve(data);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file);
        });
    }

    // Clear all data
    clearDatabase() {
        localStorage.removeItem(this.dbName);
        this.createInitialDatabase();
    }

    // Get statistics
    getStatistics() {
        const db = this.getDatabase();
        return {
            customers: db.customers.length,
            suppliers: db.suppliers.length,
            representatives: db.representatives.length,
            warehouses: db.warehouses.length,
            products: db.products.length,
            salesInvoices: db.salesInvoices.length,
            purchaseInvoices: db.purchaseInvoices.length,
            quotations: db.quotations.length,
            treasuryBalance: db.treasury.balance
        };
    }
}

// Create global database manager instance
window.dbManager = new DatabaseManager();
