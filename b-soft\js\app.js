// Sales Management System - Main Application
class SalesManagementApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSidebarToggle();
        this.initializeDatabase();

        // Load initial page based on URL hash
        const hash = window.location.hash;
        const initialPage = hash ? hash.substring(1) : 'dashboard';
        this.loadPage(initialPage);
    }

    setupEventListeners() {
        // Navigation links
        document.addEventListener('click', (e) => {
            // Check if clicked element is a navigation link or its parent
            let target = e.target;

            // Traverse up to find the actual link element
            while (target && target !== document) {
                if (target.tagName === 'A' && target.getAttribute('href') && target.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const href = target.getAttribute('href');
                    const page = href.substring(1);

                    // Only load page if it's not empty
                    if (page) {
                        this.loadPage(page);

                        // Update URL without page reload
                        if (window.history && window.history.pushState) {
                            window.history.pushState({page: page}, '', href);
                        }

                        // Close sidebar on mobile after navigation
                        if (this.isMobile && this.isMobile() && !this.sidebarCollapsed) {
                            this.hideSidebar();
                        }

                        // Close any open dropdowns
                        const dropdowns = document.querySelectorAll('.dropdown-menu.show');
                        dropdowns.forEach(dropdown => {
                            dropdown.classList.remove('show');
                        });
                    }
                    break;
                }
                target = target.parentElement;
            }
        });

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.loadPage(e.state.page);
            } else {
                // Get page from current URL hash
                const hash = window.location.hash;
                const page = hash ? hash.substring(1) : 'dashboard';
                this.loadPage(page);
            }
        });

        // Prevent default behavior for hash links
        document.addEventListener('DOMContentLoaded', () => {
            // Override default hash link behavior
            const style = document.createElement('style');
            style.textContent = `
                a[href^="#"]:not([href="#"]) {
                    scroll-behavior: auto;
                }
            `;
            document.head.appendChild(style);
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmission(e);
        });

        // Dynamic form updates
        document.addEventListener('change', (e) => {
            this.handleFormChanges(e);
        });
    }

    setupSidebarToggle() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        if (!sidebarToggle || !sidebar || !mainContent) return;

        // Initialize sidebar state from localStorage
        const savedState = localStorage.getItem('sidebarCollapsed');
        this.sidebarCollapsed = savedState === 'true';

        // Toggle button click event
        sidebarToggle.addEventListener('click', () => {
            this.toggleSidebar();
        });

        // Overlay click event (for mobile)
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => {
                this.hideSidebar();
            });
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isMobile() && !this.sidebarCollapsed) {
                this.hideSidebar();
            }
        });

        // Initialize based on screen size
        setTimeout(() => {
            this.handleWindowResize();
        }, 100);
    }

    toggleSidebar() {
        if (this.isMobile()) {
            this.toggleMobileSidebar();
        } else {
            this.toggleDesktopSidebar();
        }
    }

    toggleDesktopSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const toggleButton = document.getElementById('sidebarToggle');

        this.sidebarCollapsed = !this.sidebarCollapsed;

        if (this.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
            toggleButton.title = 'إظهار القائمة الجانبية';
        } else {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
            toggleButton.innerHTML = '<i class="fas fa-times"></i>';
            toggleButton.title = 'إخفاء القائمة الجانبية';
        }

        // Save state to localStorage
        localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');

        this.sidebarCollapsed = !this.sidebarCollapsed;

        if (this.sidebarCollapsed) {
            sidebar.classList.remove('show');
            if (sidebarOverlay) sidebarOverlay.classList.remove('show');
        } else {
            sidebar.classList.add('show');
            if (sidebarOverlay) sidebarOverlay.classList.add('show');
        }
    }

    hideSidebar() {
        if (this.isMobile()) {
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            this.sidebarCollapsed = true;
            sidebar.classList.remove('show');
            if (sidebarOverlay) sidebarOverlay.classList.remove('show');
        }
    }

    isMobile() {
        return window.innerWidth <= 768;
    }

    handleWindowResize() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const toggleButton = document.getElementById('sidebarToggle');

        if (this.isMobile()) {
            // Mobile mode
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
            if (!this.sidebarCollapsed) {
                sidebar.classList.add('show');
                if (sidebarOverlay) sidebarOverlay.classList.add('show');
            } else {
                sidebar.classList.remove('show');
                if (sidebarOverlay) sidebarOverlay.classList.remove('show');
            }
            toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
            toggleButton.title = 'إظهار القائمة الجانبية';
        } else {
            // Desktop mode
            sidebar.classList.remove('show');
            if (sidebarOverlay) sidebarOverlay.classList.remove('show');

            // Restore saved state
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState !== null) {
                this.sidebarCollapsed = savedState === 'true';
            }

            if (this.sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
                toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
                toggleButton.title = 'إظهار القائمة الجانبية';
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
                toggleButton.innerHTML = '<i class="fas fa-times"></i>';
                toggleButton.title = 'إخفاء القائمة الجانبية';
            }
        }
    }

    loadPage(page) {
        this.currentPage = page;
        const contentArea = document.getElementById('content-area');

        // Update URL hash if not already set
        if (window.location.hash !== '#' + page) {
            if (window.history && window.history.replaceState) {
                window.history.replaceState({page: page}, '', '#' + page);
            } else {
                window.location.hash = page;
            }
        }

        // Update active navigation
        this.updateActiveNavigation(page);
        
        // Load page content
        switch (page) {
            case 'dashboard':
                contentArea.innerHTML = this.getDashboardHTML();
                this.loadDashboardData();
                break;
            case 'customers':
                contentArea.innerHTML = this.getCustomersHTML();
                this.loadCustomersData();
                break;
            case 'suppliers':
                contentArea.innerHTML = this.getSuppliersHTML();
                this.loadSuppliersData();
                break;
            case 'representatives':
                contentArea.innerHTML = this.getRepresentativesHTML();
                this.loadRepresentativesData();
                break;
            case 'warehouses':
                contentArea.innerHTML = this.getWarehousesHTML();
                this.loadWarehousesData();
                break;
            case 'products':
                contentArea.innerHTML = this.getProductsHTML();
                this.loadProductsData();
                setTimeout(() => this.updateProductFormPrices(), 100);
                break;
            case 'sales-invoices':
                contentArea.innerHTML = this.getSalesInvoicesHTML();
                this.loadSalesInvoicesData();
                break;
            case 'purchase-invoices':
                contentArea.innerHTML = this.getPurchaseInvoicesHTML();
                this.loadPurchaseInvoicesData();
                break;
            case 'quotations':
                contentArea.innerHTML = this.getQuotationsHTML();
                this.loadQuotationsData();
                break;
            case 'treasury':
                contentArea.innerHTML = this.getTreasuryHTML();
                this.loadTreasuryData();
                break;
            case 'reports':
                contentArea.innerHTML = this.getReportsHTML();
                this.loadReportsData();
                break;
            case 'users':
                contentArea.innerHTML = this.getUsersHTML();
                this.loadUsersData();
                break;
            case 'sales-returns':
                contentArea.innerHTML = this.getSalesReturnsHTML();
                this.loadSalesReturnsData();
                setTimeout(() => this.populateFormDropdowns(), 100);
                break;
            case 'purchase-returns':
                contentArea.innerHTML = this.getPurchaseReturnsHTML();
                this.loadPurchaseReturnsData();
                setTimeout(() => this.populateFormDropdowns(), 100);
                break;
            case 'receipt-vouchers':
                contentArea.innerHTML = this.getReceiptVouchersHTML();
                this.loadReceiptVouchersData();
                setTimeout(() => this.populateFormDropdowns(), 100);
                break;
            case 'payment-vouchers':
                contentArea.innerHTML = this.getPaymentVouchersHTML();
                this.loadPaymentVouchersData();
                setTimeout(() => this.populateFormDropdowns(), 100);
                break;
            case 'settings':
                contentArea.innerHTML = this.getSettingsHTML();
                this.loadSettingsData();
                break;
            default:
                contentArea.innerHTML = this.getDashboardHTML();
                this.loadDashboardData();
        }
        
        // Add fade-in animation
        contentArea.classList.add('fade-in');
        setTimeout(() => contentArea.classList.remove('fade-in'), 500);
    }

    updateActiveNavigation(page) {
        // Remove active class from all nav links and dropdown items
        document.querySelectorAll('.nav-link, .dropdown-item').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to current page link
        document.querySelectorAll(`a[href="#${page}"]`).forEach(link => {
            link.classList.add('active');

            // If it's a dropdown item, also highlight the parent dropdown
            const dropdown = link.closest('.dropdown');
            if (dropdown) {
                const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                if (dropdownToggle) {
                    dropdownToggle.classList.add('active');
                }
            }
        });

        // Update page title
        document.title = this.getPageTitle(page);
    }

    getPageTitle(page) {
        const titles = {
            'dashboard': 'لوحة التحكم - نظام إدارة المبيعات',
            'customers': 'العملاء - نظام إدارة المبيعات',
            'suppliers': 'الموردين - نظام إدارة المبيعات',
            'representatives': 'المناديب - نظام إدارة المبيعات',
            'warehouses': 'المخازن - نظام إدارة المبيعات',
            'products': 'الأصناف - نظام إدارة المبيعات',
            'sales-invoices': 'فواتير البيع - نظام إدارة المبيعات',
            'purchase-invoices': 'فواتير الشراء - نظام إدارة المبيعات',
            'quotations': 'عروض الأسعار - نظام إدارة المبيعات',
            'sales-returns': 'مرتجعات المبيعات - نظام إدارة المبيعات',
            'purchase-returns': 'مرتجعات المشتريات - نظام إدارة المبيعات',
            'receipt-vouchers': 'سندات القبض - نظام إدارة المبيعات',
            'payment-vouchers': 'سندات الدفع - نظام إدارة المبيعات',
            'treasury': 'الخزنة - نظام إدارة المبيعات',
            'reports': 'التقارير - نظام إدارة المبيعات',
            'users': 'المستخدمين - نظام إدارة المبيعات',
            'settings': 'الإعدادات - نظام إدارة المبيعات'
        };

        return titles[page] || 'نظام إدارة المبيعات';
    }

    getDashboardHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">لوحة التحكم</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة جديد
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="dashboard-card sales">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h3 id="total-sales">0</h3>
                                <p>إجمالي المبيعات</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-chart-line fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="dashboard-card purchases">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h3 id="total-purchases">0</h3>
                                <p>إجمالي المشتريات</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="dashboard-card inventory">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h3 id="total-products">0</h3>
                                <p>عدد الأصناف</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-boxes fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="dashboard-card treasury">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h3 id="treasury-balance">0</h3>
                                <p>رصيد الخزنة</p>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-wallet fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">آخر الفواتير</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-invoices">
                                        <tr>
                                            <td colspan="5" class="text-center">لا توجد فواتير</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الأصناف منخفضة المخزون</h5>
                        </div>
                        <div class="card-body">
                            <div id="low-stock-items">
                                <p class="text-muted text-center">لا توجد أصناف منخفضة المخزون</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getCustomersHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة العملاء</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#customerModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة عميل جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم العميل</th>
                                    <th>الهاتف</th>
                                    <th>العنوان</th>
                                    <th>الرصيد (مدين)</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customers-table">
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد عملاء</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Customer Modal -->
            <div class="modal fade" id="customerModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة عميل جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="customerForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="customerName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="customerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="customerPhone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="customerPhone">
                                </div>
                                <div class="mb-3">
                                    <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="customerEmail">
                                </div>
                                <div class="mb-3">
                                    <label for="customerAddress" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="customerCreditLimit" class="form-label">حد الائتمان</label>
                                    <input type="number" class="form-control" id="customerCreditLimit" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label for="customerOpeningBalance" class="form-label">الرصيد الافتتاحي (مدين)</label>
                                    <input type="number" class="form-control" id="customerOpeningBalance" step="0.01" placeholder="0.00">
                                    <div class="form-text">المبلغ المستحق على العميل في بداية النظام</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getSuppliersHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الموردين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#supplierModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مورد جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم المورد</th>
                                    <th>الهاتف</th>
                                    <th>العنوان</th>
                                    <th>الرصيد (دائن)</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="suppliers-table">
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد موردين</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Supplier Modal -->
            <div class="modal fade" id="supplierModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مورد جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="supplierForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="supplierName" class="form-label">اسم المورد</label>
                                    <input type="text" class="form-control" id="supplierName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="supplierPhone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="supplierPhone">
                                </div>
                                <div class="mb-3">
                                    <label for="supplierEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="supplierEmail">
                                </div>
                                <div class="mb-3">
                                    <label for="supplierAddress" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="supplierAddress" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="supplierTaxNumber" class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="supplierTaxNumber">
                                </div>
                                <div class="mb-3">
                                    <label for="supplierOpeningBalance" class="form-label">الرصيد الافتتاحي (دائن)</label>
                                    <input type="number" class="form-control" id="supplierOpeningBalance" step="0.01" placeholder="0.00">
                                    <div class="form-text">المبلغ المستحق للمورد في بداية النظام</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getRepresentativesHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المناديب</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#representativeModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مندوب جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم المندوب</th>
                                    <th>الهاتف</th>
                                    <th>المنطقة</th>
                                    <th>نسبة العمولة</th>
                                    <th>الرصيد (دائن)</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="representatives-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مناديب</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Representative Modal -->
            <div class="modal fade" id="representativeModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مندوب جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="representativeForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="representativeName" class="form-label">اسم المندوب</label>
                                    <input type="text" class="form-control" id="representativeName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="representativePhone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="representativePhone">
                                </div>
                                <div class="mb-3">
                                    <label for="representativeEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="representativeEmail">
                                </div>
                                <div class="mb-3">
                                    <label for="representativeArea" class="form-label">المنطقة</label>
                                    <input type="text" class="form-control" id="representativeArea">
                                </div>
                                <div class="mb-3">
                                    <label for="representativeCommission" class="form-label">نسبة العمولة (%)</label>
                                    <input type="number" class="form-control" id="representativeCommission" step="0.01" min="0" max="100">
                                </div>
                                <div class="mb-3">
                                    <label for="representativeOpeningBalance" class="form-label">الرصيد الافتتاحي (دائن)</label>
                                    <input type="number" class="form-control" id="representativeOpeningBalance" step="0.01" placeholder="0.00">
                                    <div class="form-text">المبلغ المستحق للمندوب في بداية النظام</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getWarehousesHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المخازن</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#warehouseModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مخزن جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم المخزن</th>
                                    <th>الموقع</th>
                                    <th>المدير</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="warehouses-table">
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد مخازن</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Warehouse Modal -->
            <div class="modal fade" id="warehouseModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مخزن جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="warehouseForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="warehouseName" class="form-label">اسم المخزن</label>
                                    <input type="text" class="form-control" id="warehouseName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="warehouseLocation" class="form-label">الموقع</label>
                                    <input type="text" class="form-control" id="warehouseLocation">
                                </div>
                                <div class="mb-3">
                                    <label for="warehouseManager" class="form-label">مدير المخزن</label>
                                    <input type="text" class="form-control" id="warehouseManager">
                                </div>
                                <div class="mb-3">
                                    <label for="warehouseDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="warehouseDescription" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getProductsHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الأصناف</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة صنف جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم الصنف</th>
                                    <th>الفئة</th>
                                    <th>سعر الشراء</th>
                                    <th>سعر البيع</th>
                                    <th id="wholesalePriceHeader" style="display: none;">سعر الجملة</th>
                                    <th id="semiWholesalePriceHeader" style="display: none;">سعر نصف الجملة</th>
                                    <th id="retailPriceHeader" style="display: none;">سعر التجزئة</th>
                                    <th>المخزون</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="products-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد أصناف</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Product Modal -->
            <div class="modal fade" id="productModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة صنف جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="productForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productName" class="form-label">اسم الصنف</label>
                                            <input type="text" class="form-control" id="productName" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productCode" class="form-label">كود الصنف</label>
                                            <input type="text" class="form-control" id="productCode">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productCategory" class="form-label">الفئة</label>
                                            <select class="form-control" id="productCategory" required>
                                                <option value="">اختر الفئة</option>
                                                <option value="1">إلكترونيات</option>
                                                <option value="2">ملابس</option>
                                                <option value="3">أغذية</option>
                                                <option value="4">أدوات منزلية</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productUnit" class="form-label">الوحدة</label>
                                            <input type="text" class="form-control" id="productUnit" placeholder="قطعة، كيلو، متر...">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productPurchasePrice" class="form-label">سعر الشراء</label>
                                            <input type="number" class="form-control" id="productPurchasePrice" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productSalePrice" class="form-label">سعر البيع (قطاعي)</label>
                                            <input type="number" class="form-control" id="productSalePrice" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row" id="wholesalePricesRow">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="productWholesalePrice" class="form-label">سعر البيع (جملة)</label>
                                            <input type="number" class="form-control" id="productWholesalePrice" step="0.01" min="0">
                                            <div class="form-text">للكميات الكبيرة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="productSemiWholesalePrice" class="form-label">سعر البيع (نصف جملة)</label>
                                            <input type="number" class="form-control" id="productSemiWholesalePrice" step="0.01" min="0">
                                            <div class="form-text">للكميات المتوسطة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="productRetailPrice" class="form-label">سعر البيع (تجزئة)</label>
                                            <input type="number" class="form-control" id="productRetailPrice" step="0.01" min="0">
                                            <div class="form-text">للكميات الصغيرة</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" id="priceQuantityLimits">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="wholesaleMinQty" class="form-label">الحد الأدنى للجملة</label>
                                            <input type="number" class="form-control" id="wholesaleMinQty" min="1" placeholder="100">
                                            <div class="form-text">أقل كمية للسعر جملة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="semiWholesaleMinQty" class="form-label">الحد الأدنى لنصف الجملة</label>
                                            <input type="number" class="form-control" id="semiWholesaleMinQty" min="1" placeholder="50">
                                            <div class="form-text">أقل كمية للسعر نصف جملة</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="retailMaxQty" class="form-label">الحد الأقصى للتجزئة</label>
                                            <input type="number" class="form-control" id="retailMaxQty" min="1" placeholder="10">
                                            <div class="form-text">أكبر كمية للسعر تجزئة</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productMinStock" class="form-label">الحد الأدنى للمخزون</label>
                                            <input type="number" class="form-control" id="productMinStock" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="productMaxStock" class="form-label">الحد الأقصى للمخزون</label>
                                            <input type="number" class="form-control" id="productMaxStock" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="productDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="productDescription" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getSalesInvoicesHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">فواتير البيع</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="app.printSalesInvoicesReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تصدير PDF
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="app.printSalesInvoicesList()">
                            <i class="fas fa-print me-1"></i>
                            طباعة القائمة
                        </button>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#salesInvoiceModal">
                        <i class="fas fa-plus me-1"></i>
                        فاتورة جديدة
                    </button>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3">
                    <button class="btn btn-success w-100" onclick="app.createSalesInvoice('cash')">
                        <i class="fas fa-money-bill-wave me-1"></i>
                        فاتورة نقدي
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100" onclick="app.createSalesInvoice('credit')">
                        <i class="fas fa-credit-card me-1"></i>
                        فاتورة آجل
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100" onclick="app.createSalesInvoice('installment')">
                        <i class="fas fa-calendar-alt me-1"></i>
                        فاتورة تقسيط
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary w-100" onclick="app.createQuotation()">
                        <i class="fas fa-file-alt me-1"></i>
                        عرض سعر
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="sales-invoices-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد فواتير</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    getPurchaseInvoicesHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">فواتير الشراء</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        فاتورة شراء جديدة
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="purchase-invoices-table">
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد فواتير شراء</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    getQuotationsHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">عروض الأسعار</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        عرض سعر جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم العرض</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="quotations-table">
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد عروض أسعار</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    getTreasuryHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الخزنة</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#incomeModal">
                            <i class="fas fa-plus me-1"></i>
                            إيراد
                        </button>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#expenseModal">
                            <i class="fas fa-minus me-1"></i>
                            مصروف
                        </button>
                    </div>
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-primary" onclick="app.printTreasuryReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تقرير PDF
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">رصيد الخزنة</h5>
                            <h2 class="text-primary" id="treasury-current-balance">0.00 ج.م</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي الإيرادات</h5>
                            <h2 class="text-success" id="treasury-total-income">0.00 ج.م</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي المصروفات</h5>
                            <h2 class="text-danger" id="treasury-total-expenses">0.00 ج.م</h2>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">حركات الخزنة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                    <th>المبلغ</th>
                                    <th>الرصيد</th>
                                </tr>
                            </thead>
                            <tbody id="treasury-transactions-table">
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد حركات</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Income Modal -->
            <div class="modal fade" id="incomeModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة إيراد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="incomeForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="incomeAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="incomeAmount" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label for="incomeDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="incomeDescription" rows="3" required></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-success">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Expense Modal -->
            <div class="modal fade" id="expenseModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مصروف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="expenseForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="expenseAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="expenseAmount" step="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label for="expenseDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="expenseDescription" rows="3" required></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-danger">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getReportsHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">التقارير</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-success" onclick="app.printSalesReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تقرير المبيعات PDF
                        </button>
                        <button type="button" class="btn btn-info" onclick="app.printPurchasesReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تقرير المشتريات PDF
                        </button>
                    </div>
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-warning" onclick="app.printInventoryReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تقرير المخزون PDF
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="app.printBalancesReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تقرير الأرصدة PDF
                        </button>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="app.printAllReports()">
                        <i class="fas fa-download me-1"></i>
                        تصدير جميع التقارير
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">تقرير المبيعات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="salesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">تقرير المشتريات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="purchasesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">الأصناف الأكثر مبيعاً</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الصنف</th>
                                            <th>الكمية المباعة</th>
                                            <th>إجمالي المبيعات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="top-products-table">
                                        <tr>
                                            <td colspan="3" class="text-center">لا توجد بيانات</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">ملخص الأرصدة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>النوع</th>
                                            <th>العدد</th>
                                            <th>إجمالي الرصيد</th>
                                        </tr>
                                    </thead>
                                    <tbody id="balances-summary-table">
                                        <tr>
                                            <td colspan="3" class="text-center">لا توجد بيانات</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getUsersHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المستخدمين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="users-table">
                                <tr>
                                    <td colspan="6" class="text-center">لا توجد مستخدمين</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- User Modal -->
            <div class="modal fade" id="userModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مستخدم جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="userForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="userFullName" class="form-label">الاسم الكامل</label>
                                    <input type="text" class="form-control" id="userFullName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="userEmail" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="userEmail">
                                </div>
                                <div class="mb-3">
                                    <label for="userPassword" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="userPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label for="userRole" class="form-label">الدور</label>
                                    <select class="form-control" id="userRole" required>
                                        <option value="">اختر الدور</option>
                                        <option value="admin">مدير</option>
                                        <option value="sales">مبيعات</option>
                                        <option value="warehouse">مخازن</option>
                                        <option value="accountant">محاسب</option>
                                    </select>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getSalesReturnsHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">مرتجعات المبيعات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="app.printSalesReturnsReport()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تقرير PDF
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#salesReturnModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مرتجع جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>رقم الفاتورة الأصلية</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>السبب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="sales-returns-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مرتجعات</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Sales Return Modal -->
            <div class="modal fade" id="salesReturnModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مرتجع مبيعات</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="salesReturnForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="returnOriginalInvoice" class="form-label">رقم الفاتورة الأصلية</label>
                                            <select class="form-control" id="returnOriginalInvoice" required>
                                                <option value="">اختر الفاتورة</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="returnDate" class="form-label">تاريخ المرتجع</label>
                                            <input type="date" class="form-control" id="returnDate" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="returnReason" class="form-label">سبب المرتجع</label>
                                    <select class="form-control" id="returnReason" required>
                                        <option value="">اختر السبب</option>
                                        <option value="defective">عيب في المنتج</option>
                                        <option value="wrong_item">صنف خاطئ</option>
                                        <option value="customer_request">طلب العميل</option>
                                        <option value="damaged">تلف أثناء النقل</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="returnNotes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="returnNotes" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الأصناف المرتجعة</label>
                                    <div id="returnItems">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ المرتجع</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getPurchaseReturnsHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">مرتجعات المشتريات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#purchaseReturnModal">
                        <i class="fas fa-plus me-1"></i>
                        إضافة مرتجع جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المرتجع</th>
                                    <th>رقم الفاتورة الأصلية</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>السبب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="purchase-returns-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد مرتجعات</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Purchase Return Modal -->
            <div class="modal fade" id="purchaseReturnModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مرتجع مشتريات</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="purchaseReturnForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="purchaseReturnOriginalInvoice" class="form-label">رقم الفاتورة الأصلية</label>
                                            <select class="form-control" id="purchaseReturnOriginalInvoice" required>
                                                <option value="">اختر الفاتورة</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="purchaseReturnDate" class="form-label">تاريخ المرتجع</label>
                                            <input type="date" class="form-control" id="purchaseReturnDate" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="purchaseReturnReason" class="form-label">سبب المرتجع</label>
                                    <select class="form-control" id="purchaseReturnReason" required>
                                        <option value="">اختر السبب</option>
                                        <option value="defective">عيب في المنتج</option>
                                        <option value="wrong_item">صنف خاطئ</option>
                                        <option value="damaged">تلف أثناء النقل</option>
                                        <option value="expired">منتهي الصلاحية</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="purchaseReturnNotes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="purchaseReturnNotes" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الأصناف المرتجعة</label>
                                    <div id="purchaseReturnItems">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ المرتجع</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getReceiptVouchersHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">سندات القبض</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#receiptVoucherModal">
                        <i class="fas fa-plus me-1"></i>
                        سند قبض جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم السند</th>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>البيان</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="receipt-vouchers-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد سندات قبض</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Receipt Voucher Modal -->
            <div class="modal fade" id="receiptVoucherModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">سند قبض جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="receiptVoucherForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="receiptDate" class="form-label">التاريخ</label>
                                            <input type="date" class="form-control" id="receiptDate" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="receiptAmount" class="form-label">المبلغ</label>
                                            <input type="number" class="form-control" id="receiptAmount" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="receiptCustomer" class="form-label">العميل</label>
                                    <select class="form-control" id="receiptCustomer" required>
                                        <option value="">اختر العميل</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="receiptPaymentMethod" class="form-label">طريقة الدفع</label>
                                    <select class="form-control" id="receiptPaymentMethod" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash">نقدي</option>
                                        <option value="check">شيك</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                        <option value="credit_card">بطاقة ائتمان</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="checkDetailsDiv" style="display: none;">
                                    <label for="receiptCheckNumber" class="form-label">رقم الشيك</label>
                                    <input type="text" class="form-control" id="receiptCheckNumber">
                                </div>
                                <div class="mb-3">
                                    <label for="receiptDescription" class="form-label">البيان</label>
                                    <textarea class="form-control" id="receiptDescription" rows="3" required></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ السند</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getPaymentVouchersHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">سندات الدفع</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentVoucherModal">
                        <i class="fas fa-plus me-1"></i>
                        سند دفع جديد
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم السند</th>
                                    <th>التاريخ</th>
                                    <th>المستفيد</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>البيان</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="payment-vouchers-table">
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد سندات دفع</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Payment Voucher Modal -->
            <div class="modal fade" id="paymentVoucherModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">سند دفع جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="paymentVoucherForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="paymentDate" class="form-label">التاريخ</label>
                                            <input type="date" class="form-control" id="paymentDate" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="paymentAmount" class="form-label">المبلغ</label>
                                            <input type="number" class="form-control" id="paymentAmount" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="paymentBeneficiaryType" class="form-label">نوع المستفيد</label>
                                    <select class="form-control" id="paymentBeneficiaryType" required>
                                        <option value="">اختر النوع</option>
                                        <option value="supplier">مورد</option>
                                        <option value="representative">مندوب</option>
                                        <option value="employee">موظف</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="paymentBeneficiaryDiv">
                                    <label for="paymentBeneficiary" class="form-label">المستفيد</label>
                                    <select class="form-control" id="paymentBeneficiary">
                                        <option value="">اختر المستفيد</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="paymentOtherBeneficiaryDiv" style="display: none;">
                                    <label for="paymentOtherBeneficiary" class="form-label">اسم المستفيد</label>
                                    <input type="text" class="form-control" id="paymentOtherBeneficiary">
                                </div>
                                <div class="mb-3">
                                    <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                                    <select class="form-control" id="paymentMethod" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash">نقدي</option>
                                        <option value="check">شيك</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="paymentCheckDetailsDiv" style="display: none;">
                                    <label for="paymentCheckNumber" class="form-label">رقم الشيك</label>
                                    <input type="text" class="form-control" id="paymentCheckNumber">
                                </div>
                                <div class="mb-3">
                                    <label for="paymentDescription" class="form-label">البيان</label>
                                    <textarea class="form-control" id="paymentDescription" rows="3" required></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="submit" class="btn btn-primary">حفظ السند</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    getSettingsHTML() {
        return `
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات النظام</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-success" onclick="app.saveSettings()">
                        <i class="fas fa-save me-1"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إعدادات الشركة</h5>
                        </div>
                        <div class="card-body">
                            <form id="companySettingsForm">
                                <div class="mb-3">
                                    <label for="companyName" class="form-label">اسم الشركة</label>
                                    <input type="text" class="form-control" id="companyName">
                                </div>
                                <div class="mb-3">
                                    <label for="companyAddress" class="form-label">عنوان الشركة</label>
                                    <textarea class="form-control" id="companyAddress" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="companyPhone" class="form-label">هاتف الشركة</label>
                                    <input type="tel" class="form-control" id="companyPhone">
                                </div>
                                <div class="mb-3">
                                    <label for="companyEmail" class="form-label">بريد الشركة الإلكتروني</label>
                                    <input type="email" class="form-control" id="companyEmail">
                                </div>
                                <div class="mb-3">
                                    <label for="taxRate" class="form-label">نسبة الضريبة (%)</label>
                                    <input type="number" class="form-control" id="taxRate" step="0.01" min="0" max="100">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إعدادات الأسعار</h5>
                        </div>
                        <div class="card-body">
                            <form id="priceSettingsForm">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableMultiplePrices">
                                        <label class="form-check-label" for="enableMultiplePrices">
                                            تفعيل الأسعار المتعددة
                                        </label>
                                    </div>
                                    <div class="form-text">تفعيل أسعار الجملة ونصف الجملة والتجزئة</div>
                                </div>

                                <div id="multiplePricesSettings" style="display: none;">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableWholesalePrice">
                                            <label class="form-check-label" for="enableWholesalePrice">
                                                تفعيل سعر الجملة
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableSemiWholesalePrice">
                                            <label class="form-check-label" for="enableSemiWholesalePrice">
                                                تفعيل سعر نصف الجملة
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableRetailPrice">
                                            <label class="form-check-label" for="enableRetailPrice">
                                                تفعيل سعر التجزئة
                                            </label>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="mb-3">
                                        <label for="defaultWholesaleMinQty" class="form-label">الحد الأدنى الافتراضي للجملة</label>
                                        <input type="number" class="form-control" id="defaultWholesaleMinQty" min="1" value="100">
                                    </div>

                                    <div class="mb-3">
                                        <label for="defaultSemiWholesaleMinQty" class="form-label">الحد الأدنى الافتراضي لنصف الجملة</label>
                                        <input type="number" class="form-control" id="defaultSemiWholesaleMinQty" min="1" value="50">
                                    </div>

                                    <div class="mb-3">
                                        <label for="defaultRetailMaxQty" class="form-label">الحد الأقصى الافتراضي للتجزئة</label>
                                        <input type="number" class="form-control" id="defaultRetailMaxQty" min="1" value="10">
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إعدادات الترقيم</h5>
                        </div>
                        <div class="card-body">
                            <form id="numberingSettingsForm">
                                <div class="mb-3">
                                    <label for="invoicePrefix" class="form-label">بادئة فواتير البيع</label>
                                    <input type="text" class="form-control" id="invoicePrefix" placeholder="INV-">
                                </div>
                                <div class="mb-3">
                                    <label for="purchaseInvoicePrefix" class="form-label">بادئة فواتير الشراء</label>
                                    <input type="text" class="form-control" id="purchaseInvoicePrefix" placeholder="PI-">
                                </div>
                                <div class="mb-3">
                                    <label for="quotationPrefix" class="form-label">بادئة عروض الأسعار</label>
                                    <input type="text" class="form-control" id="quotationPrefix" placeholder="QUO-">
                                </div>
                                <div class="mb-3">
                                    <label for="receiptVoucherPrefix" class="form-label">بادئة سندات القبض</label>
                                    <input type="text" class="form-control" id="receiptVoucherPrefix" placeholder="RV-">
                                </div>
                                <div class="mb-3">
                                    <label for="paymentVoucherPrefix" class="form-label">بادئة سندات الدفع</label>
                                    <input type="text" class="form-control" id="paymentVoucherPrefix" placeholder="PV-">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إعدادات أخرى</h5>
                        </div>
                        <div class="card-body">
                            <form id="otherSettingsForm">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-control" id="currency">
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableLowStockAlerts">
                                        <label class="form-check-label" for="enableLowStockAlerts">
                                            تفعيل تنبيهات المخزون المنخفض
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="lowStockThreshold" class="form-label">حد التنبيه للمخزون المنخفض</label>
                                    <input type="number" class="form-control" id="lowStockThreshold" min="0" value="10">
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableAutoBackup">
                                        <label class="form-check-label" for="enableAutoBackup">
                                            تفعيل النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeDatabase() {
        // Initialize database if not exists
        if (!localStorage.getItem('salesDB')) {
            const initialDB = {
                customers: [],
                suppliers: [],
                representatives: [],
                warehouses: [],
                products: [],
                salesInvoices: [],
                purchaseInvoices: [],
                quotations: [],
                salesReturns: [],
                purchaseReturns: [],
                receiptVouchers: [],
                paymentVouchers: [],
                treasury: { balance: 0, transactions: [] },
                users: [
                    {
                        id: 1,
                        username: 'admin',
                        password: 'admin123',
                        name: 'المدير العام',
                        role: 'admin',
                        permissions: ['all']
                    }
                ],
                settings: {
                    companyName: 'شركة إدارة المبيعات',
                    companyAddress: 'العنوان الرئيسي للشركة',
                    companyPhone: '01234567890',
                    companyEmail: '<EMAIL>',
                    taxRate: 14,
                    currency: 'EGP',
                    invoicePrefix: 'INV-',
                    purchaseInvoicePrefix: 'PI-',
                    quotationPrefix: 'QUO-',
                    receiptVoucherPrefix: 'RV-',
                    paymentVoucherPrefix: 'PV-',
                    purchaseOrderPrefix: 'PO-',
                    // Price settings
                    enableMultiplePrices: true,
                    enableWholesalePrice: true,
                    enableSemiWholesalePrice: true,
                    enableRetailPrice: true,
                    defaultWholesaleMinQty: 100,
                    defaultSemiWholesaleMinQty: 50,
                    defaultRetailMaxQty: 10,
                    // Other settings
                    enableLowStockAlerts: true,
                    lowStockThreshold: 10,
                    enableAutoBackup: false
                }
            };
            localStorage.setItem('salesDB', JSON.stringify(initialDB));
        }
    }

    loadDashboardData() {
        const db = this.getDatabase();
        
        // Update dashboard statistics
        document.getElementById('total-sales').textContent = this.formatCurrency(this.calculateTotalSales(db));
        document.getElementById('total-purchases').textContent = this.formatCurrency(this.calculateTotalPurchases(db));
        document.getElementById('total-products').textContent = db.products.length;
        document.getElementById('treasury-balance').textContent = this.formatCurrency(db.treasury.balance);
        
        // Load recent invoices
        this.loadRecentInvoices(db);
        
        // Load low stock items
        this.loadLowStockItems(db);
    }

    loadCustomersData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('customers-table');
        
        if (db.customers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد عملاء</td></tr>';
            return;
        }
        
        tbody.innerHTML = db.customers.map(customer => `
            <tr>
                <td>${customer.id}</td>
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>
                    <span class="${customer.balance > 0 ? 'text-danger' : 'text-success'}">
                        ${this.formatCurrency(Math.abs(customer.balance || 0))}
                        ${customer.balance > 0 ? ' (مدين)' : customer.balance < 0 ? ' (دائن)' : ''}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="app.editCustomer(${customer.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteCustomer(${customer.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    loadSuppliersData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('suppliers-table');

        if (db.suppliers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد موردين</td></tr>';
            return;
        }

        tbody.innerHTML = db.suppliers.map(supplier => `
            <tr>
                <td>${supplier.id}</td>
                <td>${supplier.name}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.address || '-'}</td>
                <td>
                    <span class="${supplier.balance > 0 ? 'text-success' : 'text-danger'}">
                        ${this.formatCurrency(Math.abs(supplier.balance || 0))}
                        ${supplier.balance > 0 ? ' (دائن)' : supplier.balance < 0 ? ' (مدين)' : ''}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="app.editSupplier(${supplier.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteSupplier(${supplier.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    loadRepresentativesData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('representatives-table');

        if (db.representatives.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مناديب</td></tr>';
            return;
        }

        tbody.innerHTML = db.representatives.map(rep => `
            <tr>
                <td>${rep.id}</td>
                <td>${rep.name}</td>
                <td>${rep.phone || '-'}</td>
                <td>${rep.area || '-'}</td>
                <td>${rep.commission || 0}%</td>
                <td>
                    <span class="${rep.balance > 0 ? 'text-success' : 'text-danger'}">
                        ${this.formatCurrency(Math.abs(rep.balance || 0))}
                        ${rep.balance > 0 ? ' (دائن)' : rep.balance < 0 ? ' (مدين)' : ''}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="app.editRepresentative(${rep.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteRepresentative(${rep.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    loadWarehousesData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('warehouses-table');

        if (db.warehouses.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد مخازن</td></tr>';
            return;
        }

        tbody.innerHTML = db.warehouses.map(warehouse => `
            <tr>
                <td>${warehouse.id}</td>
                <td>${warehouse.name}</td>
                <td>${warehouse.location || '-'}</td>
                <td>${warehouse.manager || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="app.editWarehouse(${warehouse.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteWarehouse(${warehouse.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    getDatabase() {
        return JSON.parse(localStorage.getItem('salesDB'));
    }

    saveDatabase(db) {
        localStorage.setItem('salesDB', JSON.stringify(db));
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP'
        }).format(amount);
    }

    calculateTotalSales(db) {
        return db.salesInvoices.reduce((total, invoice) => total + (invoice.total || 0), 0);
    }

    calculateTotalPurchases(db) {
        return db.purchaseInvoices.reduce((total, invoice) => total + (invoice.total || 0), 0);
    }

    loadRecentInvoices(db) {
        // Implementation for loading recent invoices
    }

    loadLowStockItems(db) {
        // Implementation for loading low stock items
    }

    handleFormSubmission(e) {
        const form = e.target;
        const formId = form.id;
        
        switch (formId) {
            case 'customerForm':
                this.handleCustomerForm(form);
                break;
            case 'supplierForm':
                this.handleSupplierForm(form);
                break;
            case 'representativeForm':
                this.handleRepresentativeForm(form);
                break;
            case 'warehouseForm':
                this.handleWarehouseForm(form);
                break;
            case 'productForm':
                this.handleProductForm(form);
                break;
            case 'salesReturnForm':
                this.handleSalesReturnForm(form);
                break;
            case 'purchaseReturnForm':
                this.handlePurchaseReturnForm(form);
                break;
            case 'receiptVoucherForm':
                this.handleReceiptVoucherForm(form);
                break;
            case 'paymentVoucherForm':
                this.handlePaymentVoucherForm(form);
                break;
            // Add other form handlers
        }
    }

    handleCustomerForm(form) {
        const formData = new FormData(form);
        const db = this.getDatabase();
        
        const openingBalance = parseFloat(document.getElementById('customerOpeningBalance').value) || 0;

        const customer = {
            id: db.customers.length + 1,
            name: document.getElementById('customerName').value,
            phone: document.getElementById('customerPhone').value,
            email: document.getElementById('customerEmail').value,
            address: document.getElementById('customerAddress').value,
            creditLimit: parseFloat(document.getElementById('customerCreditLimit').value) || 0,
            balance: openingBalance, // الرصيد الافتتاحي (مدين)
            openingBalance: openingBalance,
            createdAt: new Date().toISOString()
        };
        
        db.customers.push(customer);

        // إضافة قيد افتتاحي في الخزنة إذا كان هناك رصيد افتتاحي
        if (openingBalance > 0) {
            this.addOpeningBalanceTransaction(db, 'customer', customer.name, openingBalance, 'مدين');
        } else if (openingBalance < 0) {
            this.addOpeningBalanceTransaction(db, 'customer', customer.name, Math.abs(openingBalance), 'دائن');
        }

        this.saveDatabase(db);

        // Close modal and refresh data
        const modal = bootstrap.Modal.getInstance(document.getElementById('customerModal'));
        modal.hide();
        form.reset();
        this.loadCustomersData();

        this.showAlert('تم إضافة العميل بنجاح', 'success');
    }

    handleSupplierForm(form) {
        const db = this.getDatabase();

        const openingBalance = parseFloat(document.getElementById('supplierOpeningBalance').value) || 0;

        const supplier = {
            id: db.suppliers.length + 1,
            name: document.getElementById('supplierName').value,
            phone: document.getElementById('supplierPhone').value,
            email: document.getElementById('supplierEmail').value,
            address: document.getElementById('supplierAddress').value,
            taxNumber: document.getElementById('supplierTaxNumber').value,
            balance: openingBalance, // الرصيد الافتتاحي (دائن)
            openingBalance: openingBalance,
            createdAt: new Date().toISOString()
        };

        db.suppliers.push(supplier);

        // إضافة قيد افتتاحي في الخزنة إذا كان هناك رصيد افتتاحي
        if (openingBalance > 0) {
            this.addOpeningBalanceTransaction(db, 'supplier', supplier.name, openingBalance, 'دائن');
        } else if (openingBalance < 0) {
            this.addOpeningBalanceTransaction(db, 'supplier', supplier.name, Math.abs(openingBalance), 'مدين');
        }

        this.saveDatabase(db);

        // Close modal and refresh data
        const modal = bootstrap.Modal.getInstance(document.getElementById('supplierModal'));
        modal.hide();
        form.reset();
        this.loadSuppliersData();

        this.showAlert('تم إضافة المورد بنجاح', 'success');
    }

    handleRepresentativeForm(form) {
        const db = this.getDatabase();

        const openingBalance = parseFloat(document.getElementById('representativeOpeningBalance').value) || 0;

        const representative = {
            id: db.representatives.length + 1,
            name: document.getElementById('representativeName').value,
            phone: document.getElementById('representativePhone').value,
            email: document.getElementById('representativeEmail').value,
            area: document.getElementById('representativeArea').value,
            commission: parseFloat(document.getElementById('representativeCommission').value) || 0,
            balance: openingBalance, // الرصيد الافتتاحي (دائن)
            openingBalance: openingBalance,
            createdAt: new Date().toISOString()
        };

        db.representatives.push(representative);

        // إضافة قيد افتتاحي في الخزنة إذا كان هناك رصيد افتتاحي
        if (openingBalance > 0) {
            this.addOpeningBalanceTransaction(db, 'representative', representative.name, openingBalance, 'دائن');
        } else if (openingBalance < 0) {
            this.addOpeningBalanceTransaction(db, 'representative', representative.name, Math.abs(openingBalance), 'مدين');
        }

        this.saveDatabase(db);

        // Close modal and refresh data
        const modal = bootstrap.Modal.getInstance(document.getElementById('representativeModal'));
        modal.hide();
        form.reset();
        this.loadRepresentativesData();

        this.showAlert('تم إضافة المندوب بنجاح', 'success');
    }

    handleWarehouseForm(form) {
        const db = this.getDatabase();

        const warehouse = {
            id: db.warehouses.length + 1,
            name: document.getElementById('warehouseName').value,
            location: document.getElementById('warehouseLocation').value,
            manager: document.getElementById('warehouseManager').value,
            description: document.getElementById('warehouseDescription').value,
            createdAt: new Date().toISOString()
        };

        db.warehouses.push(warehouse);
        this.saveDatabase(db);

        // Close modal and refresh data
        const modal = bootstrap.Modal.getInstance(document.getElementById('warehouseModal'));
        modal.hide();
        form.reset();
        this.loadWarehousesData();

        this.showAlert('تم إضافة المخزن بنجاح', 'success');
    }

    handleProductForm(form) {
        const db = this.getDatabase();

        const product = {
            id: db.products.length + 1,
            name: document.getElementById('productName').value,
            code: document.getElementById('productCode').value,
            categoryId: parseInt(document.getElementById('productCategory').value),
            unit: document.getElementById('productUnit').value,
            purchasePrice: parseFloat(document.getElementById('productPurchasePrice').value) || 0,
            salePrice: parseFloat(document.getElementById('productSalePrice').value) || 0,
            minStock: parseInt(document.getElementById('productMinStock').value) || 0,
            maxStock: parseInt(document.getElementById('productMaxStock').value) || 0,
            description: document.getElementById('productDescription').value,
            createdAt: new Date().toISOString()
        };

        // Add multiple prices if enabled
        if (db.settings && db.settings.enableMultiplePrices) {
            if (db.settings.enableWholesalePrice) {
                product.wholesalePrice = parseFloat(document.getElementById('productWholesalePrice').value) || 0;
                product.wholesaleMinQty = parseInt(document.getElementById('wholesaleMinQty').value) || db.settings.defaultWholesaleMinQty || 100;
            }

            if (db.settings.enableSemiWholesalePrice) {
                product.semiWholesalePrice = parseFloat(document.getElementById('productSemiWholesalePrice').value) || 0;
                product.semiWholesaleMinQty = parseInt(document.getElementById('semiWholesaleMinQty').value) || db.settings.defaultSemiWholesaleMinQty || 50;
            }

            if (db.settings.enableRetailPrice) {
                product.retailPrice = parseFloat(document.getElementById('productRetailPrice').value) || 0;
                product.retailMaxQty = parseInt(document.getElementById('retailMaxQty').value) || db.settings.defaultRetailMaxQty || 10;
            }
        }

        db.products.push(product);

        // Initialize inventory for all warehouses
        db.warehouses.forEach(warehouse => {
            if (!db.inventory) db.inventory = [];
            db.inventory.push({
                productId: product.id,
                warehouseId: warehouse.id,
                quantity: 0,
                lastUpdated: new Date().toISOString()
            });
        });

        this.saveDatabase(db);

        // Close modal and refresh data
        const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
        modal.hide();
        form.reset();
        this.loadProductsData();

        this.showAlert('تم إضافة الصنف بنجاح', 'success');
    }

    handleSalesReturnForm(form) {
        const db = this.getDatabase();

        const salesReturn = {
            id: (db.salesReturns?.length || 0) + 1,
            returnNumber: this.generateReturnNumber('sales'),
            originalInvoiceId: parseInt(document.getElementById('returnOriginalInvoice').value),
            customerId: this.getCustomerIdFromInvoice(db, parseInt(document.getElementById('returnOriginalInvoice').value)),
            date: document.getElementById('returnDate').value,
            reason: document.getElementById('returnReason').value,
            notes: document.getElementById('returnNotes').value,
            total: 0, // سيتم حسابه من الأصناف
            items: [], // سيتم إضافتها لاحقاً
            createdAt: new Date().toISOString()
        };

        if (!db.salesReturns) db.salesReturns = [];
        db.salesReturns.push(salesReturn);

        // إضافة قيد في الخزنة
        this.addTreasuryTransaction(db, {
            type: 'expense',
            amount: salesReturn.total,
            description: `مرتجع مبيعات - ${salesReturn.returnNumber}`,
            relatedTo: 'sales_return',
            relatedId: salesReturn.id
        });

        this.saveDatabase(db);

        const modal = bootstrap.Modal.getInstance(document.getElementById('salesReturnModal'));
        modal.hide();
        form.reset();
        this.loadSalesReturnsData();

        this.showAlert('تم إضافة مرتجع المبيعات بنجاح', 'success');
    }

    handlePurchaseReturnForm(form) {
        const db = this.getDatabase();

        const purchaseReturn = {
            id: (db.purchaseReturns?.length || 0) + 1,
            returnNumber: this.generateReturnNumber('purchase'),
            originalInvoiceId: parseInt(document.getElementById('purchaseReturnOriginalInvoice').value),
            supplierId: this.getSupplierIdFromInvoice(db, parseInt(document.getElementById('purchaseReturnOriginalInvoice').value)),
            date: document.getElementById('purchaseReturnDate').value,
            reason: document.getElementById('purchaseReturnReason').value,
            notes: document.getElementById('purchaseReturnNotes').value,
            total: 0, // سيتم حسابه من الأصناف
            items: [], // سيتم إضافتها لاحقاً
            createdAt: new Date().toISOString()
        };

        if (!db.purchaseReturns) db.purchaseReturns = [];
        db.purchaseReturns.push(purchaseReturn);

        // إضافة قيد في الخزنة
        this.addTreasuryTransaction(db, {
            type: 'income',
            amount: purchaseReturn.total,
            description: `مرتجع مشتريات - ${purchaseReturn.returnNumber}`,
            relatedTo: 'purchase_return',
            relatedId: purchaseReturn.id
        });

        this.saveDatabase(db);

        const modal = bootstrap.Modal.getInstance(document.getElementById('purchaseReturnModal'));
        modal.hide();
        form.reset();
        this.loadPurchaseReturnsData();

        this.showAlert('تم إضافة مرتجع المشتريات بنجاح', 'success');
    }

    handleReceiptVoucherForm(form) {
        const db = this.getDatabase();

        const receiptVoucher = {
            id: (db.receiptVouchers?.length || 0) + 1,
            voucherNumber: this.generateVoucherNumber('receipt'),
            date: document.getElementById('receiptDate').value,
            customerId: parseInt(document.getElementById('receiptCustomer').value),
            amount: parseFloat(document.getElementById('receiptAmount').value),
            paymentMethod: document.getElementById('receiptPaymentMethod').value,
            checkNumber: document.getElementById('receiptCheckNumber').value,
            description: document.getElementById('receiptDescription').value,
            createdAt: new Date().toISOString()
        };

        if (!db.receiptVouchers) db.receiptVouchers = [];
        db.receiptVouchers.push(receiptVoucher);

        // تحديث رصيد العميل
        if (receiptVoucher.customerId) {
            this.updateCustomerBalance(receiptVoucher.customerId, receiptVoucher.amount, 'subtract');
        }

        // إضافة قيد في الخزنة
        this.addTreasuryTransaction(db, {
            type: 'income',
            amount: receiptVoucher.amount,
            description: `سند قبض - ${receiptVoucher.voucherNumber}`,
            relatedTo: 'receipt_voucher',
            relatedId: receiptVoucher.id
        });

        this.saveDatabase(db);

        const modal = bootstrap.Modal.getInstance(document.getElementById('receiptVoucherModal'));
        modal.hide();
        form.reset();
        this.loadReceiptVouchersData();

        this.showAlert('تم إضافة سند القبض بنجاح', 'success');
    }

    handlePaymentVoucherForm(form) {
        const db = this.getDatabase();

        const paymentVoucher = {
            id: (db.paymentVouchers?.length || 0) + 1,
            voucherNumber: this.generateVoucherNumber('payment'),
            date: document.getElementById('paymentDate').value,
            beneficiaryType: document.getElementById('paymentBeneficiaryType').value,
            beneficiaryId: parseInt(document.getElementById('paymentBeneficiary').value) || null,
            beneficiaryName: document.getElementById('paymentOtherBeneficiary').value,
            amount: parseFloat(document.getElementById('paymentAmount').value),
            paymentMethod: document.getElementById('paymentMethod').value,
            checkNumber: document.getElementById('paymentCheckNumber').value,
            description: document.getElementById('paymentDescription').value,
            createdAt: new Date().toISOString()
        };

        if (!db.paymentVouchers) db.paymentVouchers = [];
        db.paymentVouchers.push(paymentVoucher);

        // تحديث رصيد المستفيد
        if (paymentVoucher.beneficiaryType === 'supplier' && paymentVoucher.beneficiaryId) {
            this.updateSupplierBalance(paymentVoucher.beneficiaryId, paymentVoucher.amount, 'subtract');
        } else if (paymentVoucher.beneficiaryType === 'representative' && paymentVoucher.beneficiaryId) {
            this.updateRepresentativeBalance(paymentVoucher.beneficiaryId, paymentVoucher.amount, 'subtract');
        }

        // إضافة قيد في الخزنة
        this.addTreasuryTransaction(db, {
            type: 'expense',
            amount: paymentVoucher.amount,
            description: `سند دفع - ${paymentVoucher.voucherNumber}`,
            relatedTo: 'payment_voucher',
            relatedId: paymentVoucher.id
        });

        this.saveDatabase(db);

        const modal = bootstrap.Modal.getInstance(document.getElementById('paymentVoucherModal'));
        modal.hide();
        form.reset();
        this.loadPaymentVouchersData();

        this.showAlert('تم إضافة سند الدفع بنجاح', 'success');
    }

    generateReturnNumber(type) {
        const db = this.getDatabase();
        const prefix = type === 'sales' ? 'SR-' : 'PR-';
        const counter = (type === 'sales' ? db.salesReturns?.length : db.purchaseReturns?.length) || 0;
        return `${prefix}${String(counter + 1).padStart(6, '0')}`;
    }

    generateVoucherNumber(type) {
        const db = this.getDatabase();
        const prefix = type === 'receipt' ? 'RV-' : 'PV-';
        const counter = (type === 'receipt' ? db.receiptVouchers?.length : db.paymentVouchers?.length) || 0;
        return `${prefix}${String(counter + 1).padStart(6, '0')}`;
    }

    getCustomerIdFromInvoice(db, invoiceId) {
        const invoice = db.salesInvoices.find(inv => inv.id === invoiceId);
        return invoice ? invoice.customerId : null;
    }

    getSupplierIdFromInvoice(db, invoiceId) {
        const invoice = db.purchaseInvoices.find(inv => inv.id === invoiceId);
        return invoice ? invoice.supplierId : null;
    }

    updateRepresentativeBalance(repId, amount, operation = 'add') {
        const db = this.getDatabase();
        const repIndex = db.representatives.findIndex(r => r.id === repId);

        if (repIndex !== -1) {
            const currentBalance = db.representatives[repIndex].balance || 0;
            db.representatives[repIndex].balance = operation === 'add'
                ? currentBalance + amount
                : currentBalance - amount;

            this.saveDatabase(db);
        }
    }

    addTreasuryTransaction(db, transactionData) {
        if (!db.treasury) {
            db.treasury = { balance: 0, transactions: [] };
        }

        const transaction = {
            id: (db.treasury.transactions.length || 0) + 1,
            ...transactionData,
            date: new Date().toISOString()
        };

        db.treasury.transactions.push(transaction);

        // Update treasury balance
        if (transaction.type === 'income') {
            db.treasury.balance += transaction.amount;
        } else {
            db.treasury.balance -= transaction.amount;
        }

        transaction.balance = db.treasury.balance;
    }

    loadProductsData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('products-table');

        if (!tbody) return;

        // Update table headers based on settings
        this.updateProductTableHeaders(db);

        const settings = db.settings || {};
        const showMultiplePrices = settings.enableMultiplePrices;

        if (db.products.length === 0) {
            const colspan = showMultiplePrices ?
                (7 + (settings.enableWholesalePrice ? 1 : 0) + (settings.enableSemiWholesalePrice ? 1 : 0) + (settings.enableRetailPrice ? 1 : 0)) : 7;
            tbody.innerHTML = `<tr><td colspan="${colspan}" class="text-center">لا توجد أصناف</td></tr>`;
            return;
        }

        tbody.innerHTML = db.products.map(product => {
            const category = db.categories.find(cat => cat.id == product.categoryId);
            const inventory = this.getProductTotalInventory(product.id, db);

            let priceColumns = '';
            if (showMultiplePrices) {
                if (settings.enableWholesalePrice) {
                    priceColumns += `<td>${this.formatCurrency(product.wholesalePrice || 0)}</td>`;
                }
                if (settings.enableSemiWholesalePrice) {
                    priceColumns += `<td>${this.formatCurrency(product.semiWholesalePrice || 0)}</td>`;
                }
                if (settings.enableRetailPrice) {
                    priceColumns += `<td>${this.formatCurrency(product.retailPrice || 0)}</td>`;
                }
            }

            return `
                <tr>
                    <td>${product.id}</td>
                    <td>${product.name}</td>
                    <td>${category ? category.name : '-'}</td>
                    <td>${this.formatCurrency(product.purchasePrice || 0)}</td>
                    <td>${this.formatCurrency(product.salePrice || 0)}</td>
                    ${priceColumns}
                    <td>
                        <span class="badge ${inventory <= (product.minStock || 0) ? 'bg-danger' : 'bg-success'}">
                            ${inventory}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.editProduct(${product.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteProduct(${product.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    updateProductTableHeaders(db) {
        const settings = db.settings || {};
        const showMultiplePrices = settings.enableMultiplePrices;

        const wholesaleHeader = document.getElementById('wholesalePriceHeader');
        const semiWholesaleHeader = document.getElementById('semiWholesalePriceHeader');
        const retailHeader = document.getElementById('retailPriceHeader');

        if (wholesaleHeader) {
            wholesaleHeader.style.display = (showMultiplePrices && settings.enableWholesalePrice) ? 'table-cell' : 'none';
        }
        if (semiWholesaleHeader) {
            semiWholesaleHeader.style.display = (showMultiplePrices && settings.enableSemiWholesalePrice) ? 'table-cell' : 'none';
        }
        if (retailHeader) {
            retailHeader.style.display = (showMultiplePrices && settings.enableRetailPrice) ? 'table-cell' : 'none';
        }
    }

    loadSalesInvoicesData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('sales-invoices-table');

        if (!tbody) return;

        if (db.salesInvoices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد فواتير</td></tr>';
            return;
        }

        tbody.innerHTML = db.salesInvoices.map(invoice => {
            const customer = db.customers.find(c => c.id == invoice.customerId);
            const statusClass = this.getInvoiceStatusClass(invoice.status);
            const typeText = this.getInvoiceTypeText(invoice.paymentType);

            return `
                <tr>
                    <td>${invoice.invoiceNumber}</td>
                    <td>${customer ? customer.name : 'عميل نقدي'}</td>
                    <td>${new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                    <td>${typeText}</td>
                    <td>${this.formatCurrency(invoice.total || 0)}</td>
                    <td>
                        <span class="badge ${statusClass}">${this.getInvoiceStatusText(invoice.status)}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewInvoice(${invoice.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="app.printInvoice(${invoice.id})">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="app.printSalesInvoicePDF(${invoice.id})">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteInvoice(${invoice.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getProductTotalInventory(productId, db) {
        if (!db.inventory) return 0;
        return db.inventory
            .filter(inv => inv.productId === productId)
            .reduce((total, inv) => total + (inv.quantity || 0), 0);
    }

    getInvoiceStatusClass(status) {
        switch (status) {
            case 'paid': return 'bg-success';
            case 'pending': return 'bg-warning';
            case 'overdue': return 'bg-danger';
            case 'draft': return 'bg-secondary';
            default: return 'bg-secondary';
        }
    }

    getInvoiceStatusText(status) {
        switch (status) {
            case 'paid': return 'مدفوعة';
            case 'pending': return 'معلقة';
            case 'overdue': return 'متأخرة';
            case 'draft': return 'مسودة';
            default: return 'غير محدد';
        }
    }

    getInvoiceTypeText(type) {
        switch (type) {
            case 'cash': return 'نقدي';
            case 'credit': return 'آجل';
            case 'installment': return 'تقسيط';
            default: return 'غير محدد';
        }
    }

    loadPurchaseInvoicesData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('purchase-invoices-table');

        if (!tbody) return;

        if (db.purchaseInvoices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد فواتير شراء</td></tr>';
            return;
        }

        tbody.innerHTML = db.purchaseInvoices.map(invoice => {
            const supplier = db.suppliers.find(s => s.id == invoice.supplierId);
            const statusClass = this.getInvoiceStatusClass(invoice.status);

            return `
                <tr>
                    <td>${invoice.invoiceNumber}</td>
                    <td>${supplier ? supplier.name : '-'}</td>
                    <td>${new Date(invoice.date).toLocaleDateString('ar-EG')}</td>
                    <td>${this.formatCurrency(invoice.total || 0)}</td>
                    <td>
                        <span class="badge ${statusClass}">${this.getInvoiceStatusText(invoice.status)}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewPurchaseInvoice(${invoice.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="app.printPurchaseInvoice(${invoice.id})">
                            <i class="fas fa-print"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    loadQuotationsData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('quotations-table');

        if (!tbody) return;

        if (db.quotations.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد عروض أسعار</td></tr>';
            return;
        }

        tbody.innerHTML = db.quotations.map(quotation => {
            const customer = db.customers.find(c => c.id == quotation.customerId);

            return `
                <tr>
                    <td>${quotation.quotationNumber}</td>
                    <td>${customer ? customer.name : '-'}</td>
                    <td>${new Date(quotation.date).toLocaleDateString('ar-EG')}</td>
                    <td>${this.formatCurrency(quotation.total || 0)}</td>
                    <td>
                        <span class="badge bg-info">${quotation.status || 'جديد'}</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewQuotation(${quotation.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="app.convertToInvoice(${quotation.id})">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    loadTreasuryData() {
        const db = this.getDatabase();

        // Update treasury summary
        const currentBalance = document.getElementById('treasury-current-balance');
        const totalIncome = document.getElementById('treasury-total-income');
        const totalExpenses = document.getElementById('treasury-total-expenses');

        if (currentBalance) currentBalance.textContent = this.formatCurrency(db.treasury.balance);

        if (db.treasury.transactions) {
            const income = db.treasury.transactions
                .filter(t => t.type === 'income')
                .reduce((sum, t) => sum + t.amount, 0);
            const expenses = db.treasury.transactions
                .filter(t => t.type === 'expense')
                .reduce((sum, t) => sum + t.amount, 0);

            if (totalIncome) totalIncome.textContent = this.formatCurrency(income);
            if (totalExpenses) totalExpenses.textContent = this.formatCurrency(expenses);
        }

        // Load transactions table
        const tbody = document.getElementById('treasury-transactions-table');
        if (!tbody) return;

        if (!db.treasury.transactions || db.treasury.transactions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد حركات</td></tr>';
            return;
        }

        tbody.innerHTML = db.treasury.transactions.slice(-20).reverse().map(transaction => `
            <tr>
                <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                <td>
                    <span class="badge ${transaction.type === 'income' ? 'bg-success' : 'bg-danger'}">
                        ${transaction.type === 'income' ? 'إيراد' : 'مصروف'}
                    </span>
                </td>
                <td>${transaction.description}</td>
                <td class="${transaction.type === 'income' ? 'text-success' : 'text-danger'}">
                    ${transaction.type === 'income' ? '+' : '-'}${this.formatCurrency(transaction.amount)}
                </td>
                <td>${this.formatCurrency(transaction.balance || 0)}</td>
            </tr>
        `).join('');
    }

    loadReportsData() {
        const db = this.getDatabase();

        // Load top products table
        const tbody = document.getElementById('top-products-table');
        if (tbody) {
            // This would need more complex logic to calculate top selling products
            tbody.innerHTML = '<tr><td colspan="3" class="text-center">لا توجد بيانات كافية</td></tr>';
        }

        // Load balances summary
        const balancesTbody = document.getElementById('balances-summary-table');
        if (balancesTbody) {
            this.loadBalancesSummary(db, balancesTbody);
        }
    }

    loadBalancesSummary(db, tbody) {
        // حساب أرصدة العملاء
        const customersDebit = db.customers.filter(c => c.balance > 0).reduce((sum, c) => sum + c.balance, 0);
        const customersCredit = Math.abs(db.customers.filter(c => c.balance < 0).reduce((sum, c) => sum + c.balance, 0));
        const customersCount = db.customers.length;

        // حساب أرصدة الموردين
        const suppliersCredit = db.suppliers.filter(s => s.balance > 0).reduce((sum, s) => sum + s.balance, 0);
        const suppliersDebit = Math.abs(db.suppliers.filter(s => s.balance < 0).reduce((sum, s) => sum + s.balance, 0));
        const suppliersCount = db.suppliers.length;

        // حساب أرصدة المناديب
        const repsCredit = db.representatives.filter(r => r.balance > 0).reduce((sum, r) => sum + r.balance, 0);
        const repsDebit = Math.abs(db.representatives.filter(r => r.balance < 0).reduce((sum, r) => sum + r.balance, 0));
        const repsCount = db.representatives.length;

        tbody.innerHTML = `
            <tr>
                <td>العملاء (مدين)</td>
                <td>${customersCount}</td>
                <td class="text-danger">${this.formatCurrency(customersDebit)}</td>
            </tr>
            <tr>
                <td>العملاء (دائن)</td>
                <td>-</td>
                <td class="text-success">${this.formatCurrency(customersCredit)}</td>
            </tr>
            <tr>
                <td>الموردين (دائن)</td>
                <td>${suppliersCount}</td>
                <td class="text-success">${this.formatCurrency(suppliersCredit)}</td>
            </tr>
            <tr>
                <td>الموردين (مدين)</td>
                <td>-</td>
                <td class="text-danger">${this.formatCurrency(suppliersDebit)}</td>
            </tr>
            <tr>
                <td>المناديب (دائن)</td>
                <td>${repsCount}</td>
                <td class="text-success">${this.formatCurrency(repsCredit)}</td>
            </tr>
            <tr>
                <td>المناديب (مدين)</td>
                <td>-</td>
                <td class="text-danger">${this.formatCurrency(repsDebit)}</td>
            </tr>
            <tr class="table-info">
                <td><strong>صافي الأرصدة</strong></td>
                <td>-</td>
                <td class="${(customersDebit - customersCredit + suppliersCredit - suppliersDebit + repsCredit - repsDebit) >= 0 ? 'text-success' : 'text-danger'}">
                    <strong>${this.formatCurrency(Math.abs(customersDebit - customersCredit + suppliersCredit - suppliersDebit + repsCredit - repsDebit))}</strong>
                </td>
            </tr>
        `;
    }

    loadUsersData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('users-table');

        if (!tbody) return;

        if (db.users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مستخدمين</td></tr>';
            return;
        }

        tbody.innerHTML = db.users.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.name}</td>
                <td>${this.getUserRoleText(user.role)}</td>
                <td>
                    <span class="badge ${user.isActive ? 'bg-success' : 'bg-danger'}">
                        ${user.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="app.editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="app.deleteUser(${user.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    getUserRoleText(role) {
        switch (role) {
            case 'admin': return 'مدير';
            case 'sales': return 'مبيعات';
            case 'warehouse': return 'مخازن';
            case 'accountant': return 'محاسب';
            default: return 'غير محدد';
        }
    }

    loadSalesReturnsData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('sales-returns-table');

        if (!tbody) return;

        if (!db.salesReturns || db.salesReturns.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد مرتجعات</td></tr>';
            return;
        }

        tbody.innerHTML = db.salesReturns.map(returnItem => {
            const originalInvoice = db.salesInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
            const customer = db.customers.find(c => c.id == returnItem.customerId);

            return `
                <tr>
                    <td>${returnItem.returnNumber}</td>
                    <td>${originalInvoice ? originalInvoice.invoiceNumber : '-'}</td>
                    <td>${customer ? customer.name : '-'}</td>
                    <td>${new Date(returnItem.date).toLocaleDateString('ar-EG')}</td>
                    <td>${this.formatCurrency(returnItem.total || 0)}</td>
                    <td>${this.getReturnReasonText(returnItem.reason)}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewSalesReturn(${returnItem.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="app.printSalesReturn(${returnItem.id})">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="app.printSalesReturnPDF(${returnItem.id})">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    loadPurchaseReturnsData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('purchase-returns-table');

        if (!tbody) return;

        if (!db.purchaseReturns || db.purchaseReturns.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد مرتجعات</td></tr>';
            return;
        }

        tbody.innerHTML = db.purchaseReturns.map(returnItem => {
            const originalInvoice = db.purchaseInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
            const supplier = db.suppliers.find(s => s.id == returnItem.supplierId);

            return `
                <tr>
                    <td>${returnItem.returnNumber}</td>
                    <td>${originalInvoice ? originalInvoice.invoiceNumber : '-'}</td>
                    <td>${supplier ? supplier.name : '-'}</td>
                    <td>${new Date(returnItem.date).toLocaleDateString('ar-EG')}</td>
                    <td>${this.formatCurrency(returnItem.total || 0)}</td>
                    <td>${this.getReturnReasonText(returnItem.reason)}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewPurchaseReturn(${returnItem.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="app.printPurchaseReturn(${returnItem.id})">
                            <i class="fas fa-print"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    loadReceiptVouchersData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('receipt-vouchers-table');

        if (!tbody) return;

        if (!db.receiptVouchers || db.receiptVouchers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد سندات قبض</td></tr>';
            return;
        }

        tbody.innerHTML = db.receiptVouchers.map(voucher => {
            const customer = db.customers.find(c => c.id == voucher.customerId);

            return `
                <tr>
                    <td>${voucher.voucherNumber}</td>
                    <td>${new Date(voucher.date).toLocaleDateString('ar-EG')}</td>
                    <td>${customer ? customer.name : voucher.customerName || '-'}</td>
                    <td>${this.formatCurrency(voucher.amount)}</td>
                    <td>${this.getPaymentMethodText(voucher.paymentMethod)}</td>
                    <td>${voucher.description}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewReceiptVoucher(${voucher.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="app.printReceiptVoucher(${voucher.id})">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="app.printReceiptVoucherPDF(${voucher.id})">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    loadPaymentVouchersData() {
        const db = this.getDatabase();
        const tbody = document.getElementById('payment-vouchers-table');

        if (!tbody) return;

        if (!db.paymentVouchers || db.paymentVouchers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد سندات دفع</td></tr>';
            return;
        }

        tbody.innerHTML = db.paymentVouchers.map(voucher => {
            let beneficiaryName = voucher.beneficiaryName || '-';

            if (voucher.beneficiaryType === 'supplier') {
                const supplier = db.suppliers.find(s => s.id == voucher.beneficiaryId);
                beneficiaryName = supplier ? supplier.name : beneficiaryName;
            } else if (voucher.beneficiaryType === 'representative') {
                const rep = db.representatives.find(r => r.id == voucher.beneficiaryId);
                beneficiaryName = rep ? rep.name : beneficiaryName;
            }

            return `
                <tr>
                    <td>${voucher.voucherNumber}</td>
                    <td>${new Date(voucher.date).toLocaleDateString('ar-EG')}</td>
                    <td>${beneficiaryName}</td>
                    <td>${this.formatCurrency(voucher.amount)}</td>
                    <td>${this.getPaymentMethodText(voucher.paymentMethod)}</td>
                    <td>${voucher.description}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="app.viewPaymentVoucher(${voucher.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="app.printPaymentVoucher(${voucher.id})">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="app.printPaymentVoucherPDF(${voucher.id})">
                            <i class="fas fa-file-pdf"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getReturnReasonText(reason) {
        switch (reason) {
            case 'defective': return 'عيب في المنتج';
            case 'wrong_item': return 'صنف خاطئ';
            case 'customer_request': return 'طلب العميل';
            case 'damaged': return 'تلف أثناء النقل';
            case 'expired': return 'منتهي الصلاحية';
            case 'other': return 'أخرى';
            default: return 'غير محدد';
        }
    }

    getPaymentMethodText(method) {
        switch (method) {
            case 'cash': return 'نقدي';
            case 'check': return 'شيك';
            case 'bank_transfer': return 'تحويل بنكي';
            case 'credit_card': return 'بطاقة ائتمان';
            default: return 'غير محدد';
        }
    }

    addOpeningBalanceTransaction(db, type, name, amount, balanceType) {
        if (!db.treasury) {
            db.treasury = { balance: 0, transactions: [] };
        }

        const typeText = type === 'customer' ? 'عميل' : type === 'supplier' ? 'مورد' : 'مندوب';
        const description = `رصيد افتتاحي ${balanceType} - ${typeText}: ${name}`;

        const transaction = {
            id: (db.treasury.transactions.length || 0) + 1,
            type: 'opening_balance',
            subType: balanceType,
            amount: amount,
            description: description,
            relatedTo: type,
            relatedName: name,
            balance: db.treasury.balance, // الرصيد لا يتأثر بالأرصدة الافتتاحية
            date: new Date().toISOString()
        };

        db.treasury.transactions.push(transaction);
    }

    handleFormChanges(e) {
        const target = e.target;

        // Handle payment method changes for vouchers
        if (target.id === 'receiptPaymentMethod') {
            const checkDiv = document.getElementById('checkDetailsDiv');
            if (checkDiv) {
                checkDiv.style.display = target.value === 'check' ? 'block' : 'none';
            }
        }

        if (target.id === 'paymentMethod') {
            const checkDiv = document.getElementById('paymentCheckDetailsDiv');
            if (checkDiv) {
                checkDiv.style.display = target.value === 'check' ? 'block' : 'none';
            }
        }

        // Handle beneficiary type changes for payment vouchers
        if (target.id === 'paymentBeneficiaryType') {
            this.updateBeneficiaryOptions(target.value);
        }

        // Load invoice items when invoice is selected for returns
        if (target.id === 'returnOriginalInvoice') {
            this.loadInvoiceItemsForReturn(target.value, 'sales');
        }

        if (target.id === 'purchaseReturnOriginalInvoice') {
            this.loadInvoiceItemsForReturn(target.value, 'purchase');
        }
    }

    updateBeneficiaryOptions(beneficiaryType) {
        const beneficiarySelect = document.getElementById('paymentBeneficiary');
        const beneficiaryDiv = document.getElementById('paymentBeneficiaryDiv');
        const otherBeneficiaryDiv = document.getElementById('paymentOtherBeneficiaryDiv');

        if (!beneficiarySelect) return;

        beneficiarySelect.innerHTML = '<option value="">اختر المستفيد</option>';

        if (beneficiaryType === 'other') {
            beneficiaryDiv.style.display = 'none';
            otherBeneficiaryDiv.style.display = 'block';
            return;
        } else {
            beneficiaryDiv.style.display = 'block';
            otherBeneficiaryDiv.style.display = 'none';
        }

        const db = this.getDatabase();

        if (beneficiaryType === 'supplier') {
            db.suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                beneficiarySelect.appendChild(option);
            });
        } else if (beneficiaryType === 'representative') {
            db.representatives.forEach(rep => {
                const option = document.createElement('option');
                option.value = rep.id;
                option.textContent = rep.name;
                beneficiarySelect.appendChild(option);
            });
        }
    }

    populateFormDropdowns() {
        const db = this.getDatabase();

        // Populate customers for receipt vouchers
        const receiptCustomerSelect = document.getElementById('receiptCustomer');
        if (receiptCustomerSelect) {
            receiptCustomerSelect.innerHTML = '<option value="">اختر العميل</option>';
            db.customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                receiptCustomerSelect.appendChild(option);
            });
        }

        // Populate invoices for returns
        const salesInvoiceSelect = document.getElementById('returnOriginalInvoice');
        if (salesInvoiceSelect) {
            salesInvoiceSelect.innerHTML = '<option value="">اختر الفاتورة</option>';
            db.salesInvoices.forEach(invoice => {
                const customer = db.customers.find(c => c.id == invoice.customerId);
                const option = document.createElement('option');
                option.value = invoice.id;
                option.textContent = `${invoice.invoiceNumber} - ${customer ? customer.name : 'عميل نقدي'}`;
                salesInvoiceSelect.appendChild(option);
            });
        }

        const purchaseInvoiceSelect = document.getElementById('purchaseReturnOriginalInvoice');
        if (purchaseInvoiceSelect) {
            purchaseInvoiceSelect.innerHTML = '<option value="">اختر الفاتورة</option>';
            db.purchaseInvoices.forEach(invoice => {
                const supplier = db.suppliers.find(s => s.id == invoice.supplierId);
                const option = document.createElement('option');
                option.value = invoice.id;
                option.textContent = `${invoice.invoiceNumber} - ${supplier ? supplier.name : 'مورد غير معروف'}`;
                purchaseInvoiceSelect.appendChild(option);
            });
        }

        // Set default dates
        const today = new Date().toISOString().split('T')[0];
        const dateInputs = ['returnDate', 'purchaseReturnDate', 'receiptDate', 'paymentDate'];
        dateInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = today;
            }
        });
    }

    loadSettingsData() {
        const db = this.getDatabase();
        const settings = db.settings || {};

        // Load company settings
        document.getElementById('companyName').value = settings.companyName || '';
        document.getElementById('companyAddress').value = settings.companyAddress || '';
        document.getElementById('companyPhone').value = settings.companyPhone || '';
        document.getElementById('companyEmail').value = settings.companyEmail || '';
        document.getElementById('taxRate').value = settings.taxRate || 0;

        // Load price settings
        document.getElementById('enableMultiplePrices').checked = settings.enableMultiplePrices || false;
        document.getElementById('enableWholesalePrice').checked = settings.enableWholesalePrice || false;
        document.getElementById('enableSemiWholesalePrice').checked = settings.enableSemiWholesalePrice || false;
        document.getElementById('enableRetailPrice').checked = settings.enableRetailPrice || false;
        document.getElementById('defaultWholesaleMinQty').value = settings.defaultWholesaleMinQty || 100;
        document.getElementById('defaultSemiWholesaleMinQty').value = settings.defaultSemiWholesaleMinQty || 50;
        document.getElementById('defaultRetailMaxQty').value = settings.defaultRetailMaxQty || 10;

        // Load numbering settings
        document.getElementById('invoicePrefix').value = settings.invoicePrefix || 'INV-';
        document.getElementById('purchaseInvoicePrefix').value = settings.purchaseInvoicePrefix || 'PI-';
        document.getElementById('quotationPrefix').value = settings.quotationPrefix || 'QUO-';
        document.getElementById('receiptVoucherPrefix').value = settings.receiptVoucherPrefix || 'RV-';
        document.getElementById('paymentVoucherPrefix').value = settings.paymentVoucherPrefix || 'PV-';

        // Load other settings
        document.getElementById('currency').value = settings.currency || 'EGP';
        document.getElementById('enableLowStockAlerts').checked = settings.enableLowStockAlerts || false;
        document.getElementById('lowStockThreshold').value = settings.lowStockThreshold || 10;
        document.getElementById('enableAutoBackup').checked = settings.enableAutoBackup || false;

        // Setup event listeners for price settings
        this.setupPriceSettingsListeners();

        // Show/hide multiple prices settings
        this.toggleMultiplePricesSettings();
    }

    setupPriceSettingsListeners() {
        const enableMultiplePrices = document.getElementById('enableMultiplePrices');
        if (enableMultiplePrices) {
            enableMultiplePrices.addEventListener('change', () => {
                this.toggleMultiplePricesSettings();
                this.updateProductFormPrices();
            });
        }

        // Add listeners for individual price type checkboxes
        ['enableWholesalePrice', 'enableSemiWholesalePrice', 'enableRetailPrice'].forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    this.updateProductFormPrices();
                });
            }
        });
    }

    toggleMultiplePricesSettings() {
        const enableMultiplePrices = document.getElementById('enableMultiplePrices');
        const multiplePricesSettings = document.getElementById('multiplePricesSettings');

        if (enableMultiplePrices && multiplePricesSettings) {
            multiplePricesSettings.style.display = enableMultiplePrices.checked ? 'block' : 'none';
        }
    }

    updateProductFormPrices() {
        const db = this.getDatabase();
        const settings = db.settings || {};

        const wholesalePricesRow = document.getElementById('wholesalePricesRow');
        const priceQuantityLimits = document.getElementById('priceQuantityLimits');

        if (wholesalePricesRow && priceQuantityLimits) {
            const showMultiplePrices = settings.enableMultiplePrices;
            wholesalePricesRow.style.display = showMultiplePrices ? 'block' : 'none';
            priceQuantityLimits.style.display = showMultiplePrices ? 'block' : 'none';

            // Update individual price fields visibility
            if (showMultiplePrices) {
                const wholesaleCol = document.querySelector('#wholesalePricesRow .col-md-4:nth-child(1)');
                const semiWholesaleCol = document.querySelector('#wholesalePricesRow .col-md-4:nth-child(2)');
                const retailCol = document.querySelector('#wholesalePricesRow .col-md-4:nth-child(3)');

                if (wholesaleCol) wholesaleCol.style.display = settings.enableWholesalePrice ? 'block' : 'none';
                if (semiWholesaleCol) semiWholesaleCol.style.display = settings.enableSemiWholesalePrice ? 'block' : 'none';
                if (retailCol) retailCol.style.display = settings.enableRetailPrice ? 'block' : 'none';
            }
        }
    }

    saveSettings() {
        const db = this.getDatabase();

        // Save company settings
        db.settings.companyName = document.getElementById('companyName').value;
        db.settings.companyAddress = document.getElementById('companyAddress').value;
        db.settings.companyPhone = document.getElementById('companyPhone').value;
        db.settings.companyEmail = document.getElementById('companyEmail').value;
        db.settings.taxRate = parseFloat(document.getElementById('taxRate').value) || 0;

        // Save price settings
        db.settings.enableMultiplePrices = document.getElementById('enableMultiplePrices').checked;
        db.settings.enableWholesalePrice = document.getElementById('enableWholesalePrice').checked;
        db.settings.enableSemiWholesalePrice = document.getElementById('enableSemiWholesalePrice').checked;
        db.settings.enableRetailPrice = document.getElementById('enableRetailPrice').checked;
        db.settings.defaultWholesaleMinQty = parseInt(document.getElementById('defaultWholesaleMinQty').value) || 100;
        db.settings.defaultSemiWholesaleMinQty = parseInt(document.getElementById('defaultSemiWholesaleMinQty').value) || 50;
        db.settings.defaultRetailMaxQty = parseInt(document.getElementById('defaultRetailMaxQty').value) || 10;

        // Save numbering settings
        db.settings.invoicePrefix = document.getElementById('invoicePrefix').value;
        db.settings.purchaseInvoicePrefix = document.getElementById('purchaseInvoicePrefix').value;
        db.settings.quotationPrefix = document.getElementById('quotationPrefix').value;
        db.settings.receiptVoucherPrefix = document.getElementById('receiptVoucherPrefix').value;
        db.settings.paymentVoucherPrefix = document.getElementById('paymentVoucherPrefix').value;

        // Save other settings
        db.settings.currency = document.getElementById('currency').value;
        db.settings.enableLowStockAlerts = document.getElementById('enableLowStockAlerts').checked;
        db.settings.lowStockThreshold = parseInt(document.getElementById('lowStockThreshold').value) || 10;
        db.settings.enableAutoBackup = document.getElementById('enableAutoBackup').checked;

        this.saveDatabase(db);
        this.showAlert('تم حفظ الإعدادات بنجاح', 'success');

        // Update product form if it's currently visible
        this.updateProductFormPrices();
    }

    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('main').insertBefore(alertDiv, document.querySelector('main').firstChild);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
}

    getProductPriceByQuantity(product, quantity) {
        const db = this.getDatabase();
        const settings = db.settings || {};

        if (!settings.enableMultiplePrices) {
            return product.salePrice || 0;
        }

        // Check for wholesale price
        if (settings.enableWholesalePrice && product.wholesalePrice &&
            quantity >= (product.wholesaleMinQty || settings.defaultWholesaleMinQty || 100)) {
            return product.wholesalePrice;
        }

        // Check for semi-wholesale price
        if (settings.enableSemiWholesalePrice && product.semiWholesalePrice &&
            quantity >= (product.semiWholesaleMinQty || settings.defaultSemiWholesaleMinQty || 50)) {
            return product.semiWholesalePrice;
        }

        // Check for retail price
        if (settings.enableRetailPrice && product.retailPrice &&
            quantity <= (product.retailMaxQty || settings.defaultRetailMaxQty || 10)) {
            return product.retailPrice;
        }

        // Default to regular sale price
        return product.salePrice || 0;
    }

    getPriceTypeByQuantity(product, quantity) {
        const db = this.getDatabase();
        const settings = db.settings || {};

        if (!settings.enableMultiplePrices) {
            return 'قطاعي';
        }

        // Check for wholesale price
        if (settings.enableWholesalePrice && product.wholesalePrice &&
            quantity >= (product.wholesaleMinQty || settings.defaultWholesaleMinQty || 100)) {
            return 'جملة';
        }

        // Check for semi-wholesale price
        if (settings.enableSemiWholesalePrice && product.semiWholesalePrice &&
            quantity >= (product.semiWholesaleMinQty || settings.defaultSemiWholesaleMinQty || 50)) {
            return 'نصف جملة';
        }

        // Check for retail price
        if (settings.enableRetailPrice && product.retailPrice &&
            quantity <= (product.retailMaxQty || settings.defaultRetailMaxQty || 10)) {
            return 'تجزئة';
        }

        // Default to regular sale price
        return 'قطاعي';
    }

    // PDF Printing Functions
    initializePDF() {
        // Check if jsPDF is available
        if (typeof window.jsPDF === 'undefined') {
            console.error('jsPDF library not loaded');
            return null;
        }

        const { jsPDF } = window.jsPDF;
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // Add Arabic font support
        doc.setFont('helvetica');
        doc.setFontSize(12);

        return doc;
    }

    addPDFHeader(doc, title) {
        const db = this.getDatabase();
        const settings = db.settings || {};

        // Company header
        doc.setFontSize(16);
        doc.text(settings.companyName || 'شركة إدارة المبيعات', 105, 20, { align: 'center' });

        doc.setFontSize(10);
        doc.text(settings.companyAddress || 'العنوان الرئيسي للشركة', 105, 30, { align: 'center' });
        doc.text(`Tel: ${settings.companyPhone || '01234567890'} | Email: ${settings.companyEmail || '<EMAIL>'}`, 105, 35, { align: 'center' });

        // Report title
        doc.setFontSize(14);
        doc.text(title, 105, 50, { align: 'center' });

        // Date
        doc.setFontSize(10);
        doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}`, 20, 60);

        // Line separator
        doc.line(20, 65, 190, 65);

        return 70; // Return Y position for content start
    }

    printSalesReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير المبيعات');

        // Sales summary
        const totalSales = this.calculateTotalSales(db);
        const salesCount = db.salesInvoices.length;

        doc.setFontSize(12);
        doc.text('ملخص المبيعات:', 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        doc.text(`عدد الفواتير: ${salesCount}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي المبيعات: ${this.formatCurrency(totalSales)}`, 20, yPos);
        yPos += 15;

        // Sales invoices table
        if (db.salesInvoices.length > 0) {
            const tableData = db.salesInvoices.map(invoice => {
                const customer = db.customers.find(c => c.id == invoice.customerId);
                return [
                    invoice.invoiceNumber,
                    customer ? customer.name : 'عميل نقدي',
                    new Date(invoice.date).toLocaleDateString('ar-EG'),
                    this.getInvoiceTypeText(invoice.paymentType),
                    this.formatCurrency(invoice.total || 0),
                    this.getInvoiceStatusText(invoice.status)
                ];
            });

            doc.autoTable({
                head: [['رقم الفاتورة', 'العميل', 'التاريخ', 'النوع', 'المبلغ', 'الحالة']],
                body: tableData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [41, 128, 185], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] }
            });
        }

        doc.save('تقرير_المبيعات.pdf');
        this.showAlert('تم إنشاء تقرير المبيعات بنجاح', 'success');
    }

    printPurchasesReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير المشتريات');

        // Purchases summary
        const totalPurchases = this.calculateTotalPurchases(db);
        const purchasesCount = db.purchaseInvoices.length;

        doc.setFontSize(12);
        doc.text('ملخص المشتريات:', 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        doc.text(`عدد الفواتير: ${purchasesCount}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي المشتريات: ${this.formatCurrency(totalPurchases)}`, 20, yPos);
        yPos += 15;

        // Purchase invoices table
        if (db.purchaseInvoices.length > 0) {
            const tableData = db.purchaseInvoices.map(invoice => {
                const supplier = db.suppliers.find(s => s.id == invoice.supplierId);
                return [
                    invoice.invoiceNumber,
                    supplier ? supplier.name : '-',
                    new Date(invoice.date).toLocaleDateString('ar-EG'),
                    this.formatCurrency(invoice.total || 0),
                    this.getInvoiceStatusText(invoice.status)
                ];
            });

            doc.autoTable({
                head: [['رقم الفاتورة', 'المورد', 'التاريخ', 'المبلغ', 'الحالة']],
                body: tableData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [231, 76, 60], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] }
            });
        }

        doc.save('تقرير_المشتريات.pdf');
        this.showAlert('تم إنشاء تقرير المشتريات بنجاح', 'success');
    }

    printInventoryReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير المخزون');

        // Inventory summary
        const totalProducts = db.products.length;
        const lowStockProducts = this.getLowStockProducts(db);

        doc.setFontSize(12);
        doc.text('ملخص المخزون:', 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        doc.text(`عدد الأصناف: ${totalProducts}`, 20, yPos);
        yPos += 7;
        doc.text(`أصناف منخفضة المخزون: ${lowStockProducts.length}`, 20, yPos);
        yPos += 15;

        // Products table
        if (db.products.length > 0) {
            const tableData = db.products.map(product => {
                const category = db.categories.find(cat => cat.id == product.categoryId);
                const inventory = this.getProductTotalInventory(product.id, db);
                return [
                    product.code || product.id,
                    product.name,
                    category ? category.name : '-',
                    product.unit || 'قطعة',
                    inventory.toString(),
                    product.minStock || 0,
                    this.formatCurrency(product.salePrice || 0)
                ];
            });

            doc.autoTable({
                head: [['الكود', 'اسم الصنف', 'الفئة', 'الوحدة', 'المخزون', 'الحد الأدنى', 'سعر البيع']],
                body: tableData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [46, 204, 113], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] }
            });
        }

        doc.save('تقرير_المخزون.pdf');
        this.showAlert('تم إنشاء تقرير المخزون بنجاح', 'success');
    }

    printBalancesReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير الأرصدة');

        // Customers balances
        doc.setFontSize(12);
        doc.text('أرصدة العملاء:', 20, yPos);
        yPos += 10;

        if (db.customers.length > 0) {
            const customersData = db.customers.map(customer => [
                customer.name,
                this.formatCurrency(Math.abs(customer.balance || 0)),
                customer.balance > 0 ? 'مدين' : customer.balance < 0 ? 'دائن' : 'متوازن'
            ]);

            doc.autoTable({
                head: [['اسم العميل', 'المبلغ', 'النوع']],
                body: customersData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [52, 152, 219], textColor: 255 }
            });

            yPos = doc.lastAutoTable.finalY + 15;
        }

        // Suppliers balances
        doc.setFontSize(12);
        doc.text('أرصدة الموردين:', 20, yPos);
        yPos += 10;

        if (db.suppliers.length > 0) {
            const suppliersData = db.suppliers.map(supplier => [
                supplier.name,
                this.formatCurrency(Math.abs(supplier.balance || 0)),
                supplier.balance > 0 ? 'دائن' : supplier.balance < 0 ? 'مدين' : 'متوازن'
            ]);

            doc.autoTable({
                head: [['اسم المورد', 'المبلغ', 'النوع']],
                body: suppliersData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [155, 89, 182], textColor: 255 }
            });
        }

        doc.save('تقرير_الأرصدة.pdf');
        this.showAlert('تم إنشاء تقرير الأرصدة بنجاح', 'success');
    }

    printSalesInvoicesReport() {
        this.printSalesReport();
    }

    printSalesInvoicesList() {
        window.print();
    }

    printAllReports() {
        // Create a comprehensive report
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'التقرير الشامل');

        // Summary section
        doc.setFontSize(14);
        doc.text('الملخص التنفيذي:', 20, yPos);
        yPos += 15;

        doc.setFontSize(10);
        doc.text(`إجمالي المبيعات: ${this.formatCurrency(this.calculateTotalSales(db))}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي المشتريات: ${this.formatCurrency(this.calculateTotalPurchases(db))}`, 20, yPos);
        yPos += 7;
        doc.text(`رصيد الخزنة: ${this.formatCurrency(db.treasury.balance)}`, 20, yPos);
        yPos += 7;
        doc.text(`عدد العملاء: ${db.customers.length}`, 20, yPos);
        yPos += 7;
        doc.text(`عدد الموردين: ${db.suppliers.length}`, 20, yPos);
        yPos += 7;
        doc.text(`عدد الأصناف: ${db.products.length}`, 20, yPos);
        yPos += 15;

        // Add page break if needed
        if (yPos > 250) {
            doc.addPage();
            yPos = 20;
        }

        // Recent transactions
        doc.setFontSize(12);
        doc.text('آخر المعاملات:', 20, yPos);
        yPos += 10;

        if (db.treasury.transactions && db.treasury.transactions.length > 0) {
            const recentTransactions = db.treasury.transactions.slice(-10).reverse();
            const transactionsData = recentTransactions.map(transaction => [
                new Date(transaction.date).toLocaleDateString('ar-EG'),
                transaction.type === 'income' ? 'إيراد' : 'مصروف',
                transaction.description,
                this.formatCurrency(transaction.amount)
            ]);

            doc.autoTable({
                head: [['التاريخ', 'النوع', 'الوصف', 'المبلغ']],
                body: transactionsData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [241, 196, 15], textColor: 0 }
            });
        }

        doc.save('التقرير_الشامل.pdf');
        this.showAlert('تم إنشاء التقرير الشامل بنجاح', 'success');
    }

    getLowStockProducts(db) {
        const threshold = db.settings?.lowStockThreshold || 10;
        return db.products.filter(product => {
            const inventory = this.getProductTotalInventory(product.id, db);
            return inventory <= threshold;
        });
    }

    // Individual document printing functions
    printSalesInvoicePDF(invoiceId) {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        const invoice = db.salesInvoices.find(inv => inv.id == invoiceId);
        if (!invoice) {
            this.showAlert('الفاتورة غير موجودة', 'error');
            return;
        }

        const customer = db.customers.find(c => c.id == invoice.customerId);
        let yPos = this.addPDFHeader(doc, 'فاتورة بيع');

        // Invoice details
        doc.setFontSize(12);
        doc.text(`رقم الفاتورة: ${invoice.invoiceNumber}`, 20, yPos);
        yPos += 7;
        doc.text(`التاريخ: ${new Date(invoice.date).toLocaleDateString('ar-EG')}`, 20, yPos);
        yPos += 7;
        doc.text(`العميل: ${customer ? customer.name : 'عميل نقدي'}`, 20, yPos);
        yPos += 7;
        doc.text(`نوع الدفع: ${this.getInvoiceTypeText(invoice.paymentType)}`, 20, yPos);
        yPos += 15;

        // Invoice items
        if (invoice.items && invoice.items.length > 0) {
            const itemsData = invoice.items.map(item => {
                const product = db.products.find(p => p.id == item.productId);
                return [
                    product ? product.name : 'منتج غير معروف',
                    item.quantity.toString(),
                    this.formatCurrency(item.price),
                    this.formatCurrency(item.total)
                ];
            });

            doc.autoTable({
                head: [['الصنف', 'الكمية', 'السعر', 'الإجمالي']],
                body: itemsData,
                startY: yPos,
                styles: { fontSize: 10, cellPadding: 3 },
                headStyles: { fillColor: [41, 128, 185], textColor: 255 }
            });

            yPos = doc.lastAutoTable.finalY + 10;
        }

        // Total
        doc.setFontSize(14);
        doc.text(`الإجمالي: ${this.formatCurrency(invoice.total)}`, 20, yPos);

        doc.save(`فاتورة_${invoice.invoiceNumber}.pdf`);
        this.showAlert('تم إنشاء الفاتورة بصيغة PDF', 'success');
    }

    printReceiptVoucherPDF(voucherId) {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        const voucher = db.receiptVouchers.find(v => v.id == voucherId);
        if (!voucher) {
            this.showAlert('السند غير موجود', 'error');
            return;
        }

        const customer = db.customers.find(c => c.id == voucher.customerId);
        let yPos = this.addPDFHeader(doc, 'سند قبض');

        // Voucher details
        doc.setFontSize(12);
        doc.text(`رقم السند: ${voucher.voucherNumber}`, 20, yPos);
        yPos += 10;
        doc.text(`التاريخ: ${new Date(voucher.date).toLocaleDateString('ar-EG')}`, 20, yPos);
        yPos += 10;
        doc.text(`العميل: ${customer ? customer.name : voucher.customerName || '-'}`, 20, yPos);
        yPos += 10;
        doc.text(`المبلغ: ${this.formatCurrency(voucher.amount)}`, 20, yPos);
        yPos += 10;
        doc.text(`طريقة الدفع: ${this.getPaymentMethodText(voucher.paymentMethod)}`, 20, yPos);
        yPos += 10;

        if (voucher.checkNumber) {
            doc.text(`رقم الشيك: ${voucher.checkNumber}`, 20, yPos);
            yPos += 10;
        }

        doc.text(`البيان: ${voucher.description}`, 20, yPos);

        doc.save(`سند_قبض_${voucher.voucherNumber}.pdf`);
        this.showAlert('تم إنشاء سند القبض بصيغة PDF', 'success');
    }

    printPaymentVoucherPDF(voucherId) {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        const voucher = db.paymentVouchers.find(v => v.id == voucherId);
        if (!voucher) {
            this.showAlert('السند غير موجود', 'error');
            return;
        }

        let beneficiaryName = voucher.beneficiaryName || '-';
        if (voucher.beneficiaryType === 'supplier') {
            const supplier = db.suppliers.find(s => s.id == voucher.beneficiaryId);
            beneficiaryName = supplier ? supplier.name : beneficiaryName;
        } else if (voucher.beneficiaryType === 'representative') {
            const rep = db.representatives.find(r => r.id == voucher.beneficiaryId);
            beneficiaryName = rep ? rep.name : beneficiaryName;
        }

        let yPos = this.addPDFHeader(doc, 'سند دفع');

        // Voucher details
        doc.setFontSize(12);
        doc.text(`رقم السند: ${voucher.voucherNumber}`, 20, yPos);
        yPos += 10;
        doc.text(`التاريخ: ${new Date(voucher.date).toLocaleDateString('ar-EG')}`, 20, yPos);
        yPos += 10;
        doc.text(`المستفيد: ${beneficiaryName}`, 20, yPos);
        yPos += 10;
        doc.text(`المبلغ: ${this.formatCurrency(voucher.amount)}`, 20, yPos);
        yPos += 10;
        doc.text(`طريقة الدفع: ${this.getPaymentMethodText(voucher.paymentMethod)}`, 20, yPos);
        yPos += 10;

        if (voucher.checkNumber) {
            doc.text(`رقم الشيك: ${voucher.checkNumber}`, 20, yPos);
            yPos += 10;
        }

        doc.text(`البيان: ${voucher.description}`, 20, yPos);

        doc.save(`سند_دفع_${voucher.voucherNumber}.pdf`);
        this.showAlert('تم إنشاء سند الدفع بصيغة PDF', 'success');
    }

    printSalesReturnPDF(returnId) {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        const returnItem = db.salesReturns.find(r => r.id == returnId);
        if (!returnItem) {
            this.showAlert('المرتجع غير موجود', 'error');
            return;
        }

        const originalInvoice = db.salesInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
        const customer = db.customers.find(c => c.id == returnItem.customerId);

        let yPos = this.addPDFHeader(doc, 'مرتجع مبيعات');

        // Return details
        doc.setFontSize(12);
        doc.text(`رقم المرتجع: ${returnItem.returnNumber}`, 20, yPos);
        yPos += 10;
        doc.text(`الفاتورة الأصلية: ${originalInvoice ? originalInvoice.invoiceNumber : '-'}`, 20, yPos);
        yPos += 10;
        doc.text(`التاريخ: ${new Date(returnItem.date).toLocaleDateString('ar-EG')}`, 20, yPos);
        yPos += 10;
        doc.text(`العميل: ${customer ? customer.name : '-'}`, 20, yPos);
        yPos += 10;
        doc.text(`سبب المرتجع: ${this.getReturnReasonText(returnItem.reason)}`, 20, yPos);
        yPos += 10;
        doc.text(`المبلغ: ${this.formatCurrency(returnItem.total)}`, 20, yPos);
        yPos += 10;

        if (returnItem.notes) {
            doc.text(`ملاحظات: ${returnItem.notes}`, 20, yPos);
        }

        doc.save(`مرتجع_${returnItem.returnNumber}.pdf`);
        this.showAlert('تم إنشاء مرتجع المبيعات بصيغة PDF', 'success');
    }

    // Print functions for existing buttons
    printInvoice(invoiceId) {
        this.printSalesInvoicePDF(invoiceId);
    }

    printReceiptVoucher(voucherId) {
        this.printReceiptVoucherPDF(voucherId);
    }

    printPaymentVoucher(voucherId) {
        this.printPaymentVoucherPDF(voucherId);
    }

    printSalesReturn(returnId) {
        this.printSalesReturnPDF(returnId);
    }

    printPurchaseReturn(returnId) {
        // Similar to sales return but for purchases
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        const returnItem = db.purchaseReturns.find(r => r.id == returnId);
        if (!returnItem) {
            this.showAlert('المرتجع غير موجود', 'error');
            return;
        }

        const originalInvoice = db.purchaseInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
        const supplier = db.suppliers.find(s => s.id == returnItem.supplierId);

        let yPos = this.addPDFHeader(doc, 'مرتجع مشتريات');

        doc.setFontSize(12);
        doc.text(`رقم المرتجع: ${returnItem.returnNumber}`, 20, yPos);
        yPos += 10;
        doc.text(`الفاتورة الأصلية: ${originalInvoice ? originalInvoice.invoiceNumber : '-'}`, 20, yPos);
        yPos += 10;
        doc.text(`التاريخ: ${new Date(returnItem.date).toLocaleDateString('ar-EG')}`, 20, yPos);
        yPos += 10;
        doc.text(`المورد: ${supplier ? supplier.name : '-'}`, 20, yPos);
        yPos += 10;
        doc.text(`سبب المرتجع: ${this.getReturnReasonText(returnItem.reason)}`, 20, yPos);
        yPos += 10;
        doc.text(`المبلغ: ${this.formatCurrency(returnItem.total)}`, 20, yPos);

        doc.save(`مرتجع_مشتريات_${returnItem.returnNumber}.pdf`);
        this.showAlert('تم إنشاء مرتجع المشتريات بصيغة PDF', 'success');
    }

    // Helper functions for calculations
    calculateTotalSales(db) {
        return db.salesInvoices.reduce((total, invoice) => total + (invoice.total || 0), 0);
    }

    calculateTotalPurchases(db) {
        return db.purchaseInvoices.reduce((total, invoice) => total + (invoice.total || 0), 0);
    }

    calculateTotalCustomersDebit(db) {
        return db.customers.filter(c => c.balance > 0).reduce((total, c) => total + c.balance, 0);
    }

    calculateTotalSuppliersCredit(db) {
        return db.suppliers.filter(s => s.balance > 0).reduce((total, s) => total + s.balance, 0);
    }

    // View and Delete functions
    viewInvoice(invoiceId) {
        const db = this.getDatabase();
        const invoice = db.salesInvoices.find(inv => inv.id == invoiceId);
        if (!invoice) {
            this.showAlert('الفاتورة غير موجودة', 'error');
            return;
        }

        // Create a modal to show invoice details
        const customer = db.customers.find(c => c.id == invoice.customerId);
        let itemsHtml = '';

        if (invoice.items && invoice.items.length > 0) {
            itemsHtml = invoice.items.map(item => {
                const product = db.products.find(p => p.id == item.productId);
                return `
                    <tr>
                        <td>${product ? product.name : 'منتج غير معروف'}</td>
                        <td>${item.quantity}</td>
                        <td>${this.formatCurrency(item.price)}</td>
                        <td>${this.formatCurrency(item.total)}</td>
                    </tr>
                `;
            }).join('');
        }

        const modalHtml = `
            <div class="modal fade" id="viewInvoiceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الفاتورة ${invoice.invoiceNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>العميل:</strong> ${customer ? customer.name : 'عميل نقدي'}
                                </div>
                                <div class="col-md-6">
                                    <strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('ar-EG')}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>نوع الدفع:</strong> ${this.getInvoiceTypeText(invoice.paymentType)}
                                </div>
                                <div class="col-md-6">
                                    <strong>الحالة:</strong> ${this.getInvoiceStatusText(invoice.status)}
                                </div>
                            </div>
                            <h6>الأصناف:</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${itemsHtml}
                                </tbody>
                            </table>
                            <div class="text-end">
                                <h5>الإجمالي: ${this.formatCurrency(invoice.total)}</h5>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="app.printSalesInvoicePDF(${invoice.id})">
                                <i class="fas fa-file-pdf me-1"></i>
                                طباعة PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('viewInvoiceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('viewInvoiceModal'));
        modal.show();
    }

    deleteInvoice(invoiceId) {
        if (!confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            return;
        }

        const db = this.getDatabase();
        const invoiceIndex = db.salesInvoices.findIndex(inv => inv.id == invoiceId);

        if (invoiceIndex === -1) {
            this.showAlert('الفاتورة غير موجودة', 'error');
            return;
        }

        db.salesInvoices.splice(invoiceIndex, 1);
        this.saveDatabase(db);
        this.loadSalesInvoicesData();
        this.showAlert('تم حذف الفاتورة بنجاح', 'success');
    }

    viewSalesReturn(returnId) {
        const db = this.getDatabase();
        const returnItem = db.salesReturns.find(ret => ret.id == returnId);
        if (!returnItem) {
            this.showAlert('المرتجع غير موجود', 'error');
            return;
        }

        const originalInvoice = db.salesInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
        const customer = db.customers.find(c => c.id == returnItem.customerId);

        const modalHtml = `
            <div class="modal fade" id="viewSalesReturnModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل مرتجع المبيعات ${returnItem.returnNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p><strong>الفاتورة الأصلية:</strong> ${originalInvoice ? originalInvoice.invoiceNumber : '-'}</p>
                            <p><strong>العميل:</strong> ${customer ? customer.name : '-'}</p>
                            <p><strong>التاريخ:</strong> ${new Date(returnItem.date).toLocaleDateString('ar-EG')}</p>
                            <p><strong>سبب المرتجع:</strong> ${this.getReturnReasonText(returnItem.reason)}</p>
                            <p><strong>المبلغ:</strong> ${this.formatCurrency(returnItem.total)}</p>
                            ${returnItem.notes ? `<p><strong>ملاحظات:</strong> ${returnItem.notes}</p>` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="app.printSalesReturnPDF(${returnItem.id})">
                                <i class="fas fa-file-pdf me-1"></i>
                                طباعة PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('viewSalesReturnModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('viewSalesReturnModal'));
        modal.show();
    }

    viewPurchaseReturn(returnId) {
        const db = this.getDatabase();
        const returnItem = db.purchaseReturns.find(ret => ret.id == returnId);
        if (!returnItem) {
            this.showAlert('المرتجع غير موجود', 'error');
            return;
        }

        const originalInvoice = db.purchaseInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
        const supplier = db.suppliers.find(s => s.id == returnItem.supplierId);

        const modalHtml = `
            <div class="modal fade" id="viewPurchaseReturnModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل مرتجع المشتريات ${returnItem.returnNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p><strong>الفاتورة الأصلية:</strong> ${originalInvoice ? originalInvoice.invoiceNumber : '-'}</p>
                            <p><strong>المورد:</strong> ${supplier ? supplier.name : '-'}</p>
                            <p><strong>التاريخ:</strong> ${new Date(returnItem.date).toLocaleDateString('ar-EG')}</p>
                            <p><strong>سبب المرتجع:</strong> ${this.getReturnReasonText(returnItem.reason)}</p>
                            <p><strong>المبلغ:</strong> ${this.formatCurrency(returnItem.total)}</p>
                            ${returnItem.notes ? `<p><strong>ملاحظات:</strong> ${returnItem.notes}</p>` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="app.printPurchaseReturn(${returnItem.id})">
                                <i class="fas fa-file-pdf me-1"></i>
                                طباعة PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('viewPurchaseReturnModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('viewPurchaseReturnModal'));
        modal.show();
    }

    viewReceiptVoucher(voucherId) {
        const db = this.getDatabase();
        const voucher = db.receiptVouchers.find(v => v.id == voucherId);
        if (!voucher) {
            this.showAlert('السند غير موجود', 'error');
            return;
        }

        const customer = db.customers.find(c => c.id == voucher.customerId);

        const modalHtml = `
            <div class="modal fade" id="viewReceiptVoucherModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل سند القبض ${voucher.voucherNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p><strong>التاريخ:</strong> ${new Date(voucher.date).toLocaleDateString('ar-EG')}</p>
                            <p><strong>العميل:</strong> ${customer ? customer.name : voucher.customerName || '-'}</p>
                            <p><strong>المبلغ:</strong> ${this.formatCurrency(voucher.amount)}</p>
                            <p><strong>طريقة الدفع:</strong> ${this.getPaymentMethodText(voucher.paymentMethod)}</p>
                            ${voucher.checkNumber ? `<p><strong>رقم الشيك:</strong> ${voucher.checkNumber}</p>` : ''}
                            <p><strong>البيان:</strong> ${voucher.description}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="app.printReceiptVoucherPDF(${voucher.id})">
                                <i class="fas fa-file-pdf me-1"></i>
                                طباعة PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('viewReceiptVoucherModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('viewReceiptVoucherModal'));
        modal.show();
    }

    viewPaymentVoucher(voucherId) {
        const db = this.getDatabase();
        const voucher = db.paymentVouchers.find(v => v.id == voucherId);
        if (!voucher) {
            this.showAlert('السند غير موجود', 'error');
            return;
        }

        let beneficiaryName = voucher.beneficiaryName || '-';
        if (voucher.beneficiaryType === 'supplier') {
            const supplier = db.suppliers.find(s => s.id == voucher.beneficiaryId);
            beneficiaryName = supplier ? supplier.name : beneficiaryName;
        } else if (voucher.beneficiaryType === 'representative') {
            const rep = db.representatives.find(r => r.id == voucher.beneficiaryId);
            beneficiaryName = rep ? rep.name : beneficiaryName;
        }

        const modalHtml = `
            <div class="modal fade" id="viewPaymentVoucherModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل سند الدفع ${voucher.voucherNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p><strong>التاريخ:</strong> ${new Date(voucher.date).toLocaleDateString('ar-EG')}</p>
                            <p><strong>المستفيد:</strong> ${beneficiaryName}</p>
                            <p><strong>المبلغ:</strong> ${this.formatCurrency(voucher.amount)}</p>
                            <p><strong>طريقة الدفع:</strong> ${this.getPaymentMethodText(voucher.paymentMethod)}</p>
                            ${voucher.checkNumber ? `<p><strong>رقم الشيك:</strong> ${voucher.checkNumber}</p>` : ''}
                            <p><strong>البيان:</strong> ${voucher.description}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="app.printPaymentVoucherPDF(${voucher.id})">
                                <i class="fas fa-file-pdf me-1"></i>
                                طباعة PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('viewPaymentVoucherModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('viewPaymentVoucherModal'));
        modal.show();
    }

    viewPurchaseInvoice(invoiceId) {
        const db = this.getDatabase();
        const invoice = db.purchaseInvoices.find(inv => inv.id == invoiceId);
        if (!invoice) {
            this.showAlert('الفاتورة غير موجودة', 'error');
            return;
        }

        const supplier = db.suppliers.find(s => s.id == invoice.supplierId);

        const modalHtml = `
            <div class="modal fade" id="viewPurchaseInvoiceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل فاتورة الشراء ${invoice.invoiceNumber}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p><strong>المورد:</strong> ${supplier ? supplier.name : '-'}</p>
                            <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('ar-EG')}</p>
                            <p><strong>المبلغ:</strong> ${this.formatCurrency(invoice.total)}</p>
                            <p><strong>الحالة:</strong> ${this.getInvoiceStatusText(invoice.status)}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('viewPurchaseInvoiceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('viewPurchaseInvoiceModal'));
        modal.show();
    }

    printSalesReturnsReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير مرتجعات المبيعات');

        // Returns summary
        const totalReturns = db.salesReturns?.reduce((total, ret) => total + (ret.total || 0), 0) || 0;
        const returnsCount = db.salesReturns?.length || 0;

        doc.setFontSize(12);
        doc.text('ملخص المرتجعات:', 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        doc.text(`عدد المرتجعات: ${returnsCount}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي قيمة المرتجعات: ${this.formatCurrency(totalReturns)}`, 20, yPos);
        yPos += 15;

        // Returns table
        if (db.salesReturns && db.salesReturns.length > 0) {
            const tableData = db.salesReturns.map(returnItem => {
                const originalInvoice = db.salesInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
                const customer = db.customers.find(c => c.id == returnItem.customerId);
                return [
                    returnItem.returnNumber,
                    originalInvoice ? originalInvoice.invoiceNumber : '-',
                    customer ? customer.name : '-',
                    new Date(returnItem.date).toLocaleDateString('ar-EG'),
                    this.getReturnReasonText(returnItem.reason),
                    this.formatCurrency(returnItem.total || 0)
                ];
            });

            doc.autoTable({
                head: [['رقم المرتجع', 'الفاتورة الأصلية', 'العميل', 'التاريخ', 'السبب', 'المبلغ']],
                body: tableData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [231, 76, 60], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] }
            });
        }

        doc.save('تقرير_مرتجعات_المبيعات.pdf');
        this.showAlert('تم إنشاء تقرير مرتجعات المبيعات بنجاح', 'success');
    }

    printPurchaseReturnsReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير مرتجعات المشتريات');

        // Returns summary
        const totalReturns = db.purchaseReturns?.reduce((total, ret) => total + (ret.total || 0), 0) || 0;
        const returnsCount = db.purchaseReturns?.length || 0;

        doc.setFontSize(12);
        doc.text('ملخص المرتجعات:', 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        doc.text(`عدد المرتجعات: ${returnsCount}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي قيمة المرتجعات: ${this.formatCurrency(totalReturns)}`, 20, yPos);
        yPos += 15;

        // Returns table
        if (db.purchaseReturns && db.purchaseReturns.length > 0) {
            const tableData = db.purchaseReturns.map(returnItem => {
                const originalInvoice = db.purchaseInvoices.find(inv => inv.id == returnItem.originalInvoiceId);
                const supplier = db.suppliers.find(s => s.id == returnItem.supplierId);
                return [
                    returnItem.returnNumber,
                    originalInvoice ? originalInvoice.invoiceNumber : '-',
                    supplier ? supplier.name : '-',
                    new Date(returnItem.date).toLocaleDateString('ar-EG'),
                    this.getReturnReasonText(returnItem.reason),
                    this.formatCurrency(returnItem.total || 0)
                ];
            });

            doc.autoTable({
                head: [['رقم المرتجع', 'الفاتورة الأصلية', 'المورد', 'التاريخ', 'السبب', 'المبلغ']],
                body: tableData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [155, 89, 182], textColor: 255 },
                alternateRowStyles: { fillColor: [245, 245, 245] }
            });
        }

        doc.save('تقرير_مرتجعات_المشتريات.pdf');
        this.showAlert('تم إنشاء تقرير مرتجعات المشتريات بنجاح', 'success');
    }

    printTreasuryReport() {
        const doc = this.initializePDF();
        if (!doc) return;

        const db = this.getDatabase();
        let yPos = this.addPDFHeader(doc, 'تقرير الخزنة');

        // Treasury summary
        const currentBalance = db.treasury?.balance || 0;
        const totalIncome = db.treasury?.transactions?.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0) || 0;
        const totalExpenses = db.treasury?.transactions?.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0) || 0;

        doc.setFontSize(12);
        doc.text('ملخص الخزنة:', 20, yPos);
        yPos += 10;

        doc.setFontSize(10);
        doc.text(`الرصيد الحالي: ${this.formatCurrency(currentBalance)}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي الإيرادات: ${this.formatCurrency(totalIncome)}`, 20, yPos);
        yPos += 7;
        doc.text(`إجمالي المصروفات: ${this.formatCurrency(totalExpenses)}`, 20, yPos);
        yPos += 15;

        // Recent transactions
        if (db.treasury?.transactions && db.treasury.transactions.length > 0) {
            const recentTransactions = db.treasury.transactions.slice(-20).reverse();
            const tableData = recentTransactions.map(transaction => [
                new Date(transaction.date).toLocaleDateString('ar-EG'),
                transaction.type === 'income' ? 'إيراد' : 'مصروف',
                transaction.description,
                this.formatCurrency(transaction.amount),
                this.formatCurrency(transaction.balance || 0)
            ]);

            doc.autoTable({
                head: [['التاريخ', 'النوع', 'الوصف', 'المبلغ', 'الرصيد']],
                body: tableData,
                startY: yPos,
                styles: { fontSize: 8, cellPadding: 2 },
                headStyles: { fillColor: [241, 196, 15], textColor: 0 },
                alternateRowStyles: { fillColor: [245, 245, 245] }
            });
        }

        doc.save('تقرير_الخزنة.pdf');
        this.showAlert('تم إنشاء تقرير الخزنة بنجاح', 'success');
    }
}

// Initialize the application
const app = new SalesManagementApp();

// Make app globally available
window.app = app;
