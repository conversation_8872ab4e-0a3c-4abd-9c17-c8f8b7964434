<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حوسبة - نظام إدارة الأعمال</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .test-card {
            margin-bottom: 1rem;
            border-radius: 0.75rem;
        }
        .test-success {
            border-left: 4px solid #28a745;
        }
        .test-error {
            border-left: 4px solid #dc3545;
        }
        .test-warning {
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-laptop-code me-2"></i>
                            اختبار حوسبة - نظام إدارة الأعمال
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري الاختبار...</span>
                                </div>
                                <p class="mt-2">جاري تشغيل الاختبارات...</p>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button class="btn btn-primary" onclick="runTests()">
                                <i class="fas fa-play me-1"></i>
                                إعادة تشغيل الاختبارات
                            </button>
                            <button class="btn btn-success" onclick="loadSampleData()">
                                <i class="fas fa-database me-1"></i>
                                تحميل البيانات التجريبية
                            </button>
                            <a href="index.html" class="btn btn-info">
                                <i class="fas fa-home me-1"></i>
                                الذهاب للتطبيق الرئيسي
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/database.js"></script>
    <script src="js/sample-data.js"></script>
    
    <script>
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">جاري تشغيل الاختبارات...</p></div>';
            
            setTimeout(() => {
                const tests = [];
                
                // Test 1: Database Manager
                try {
                    if (window.dbManager) {
                        tests.push({
                            name: 'إدارة قاعدة البيانات',
                            status: 'success',
                            message: 'تم تحميل مدير قاعدة البيانات بنجاح'
                        });
                    } else {
                        tests.push({
                            name: 'إدارة قاعدة البيانات',
                            status: 'error',
                            message: 'فشل في تحميل مدير قاعدة البيانات'
                        });
                    }
                } catch (e) {
                    tests.push({
                        name: 'إدارة قاعدة البيانات',
                        status: 'error',
                        message: 'خطأ: ' + e.message
                    });
                }
                
                // Test 2: Database Creation
                try {
                    const db = window.dbManager.getDatabase();
                    if (db && db.settings && db.settings.companyName === 'حوسبة - نظام إدارة الأعمال') {
                        tests.push({
                            name: 'إنشاء قاعدة البيانات',
                            status: 'success',
                            message: 'تم إنشاء قاعدة البيانات مع الإعدادات الصحيحة'
                        });
                    } else {
                        tests.push({
                            name: 'إنشاء قاعدة البيانات',
                            status: 'warning',
                            message: 'قاعدة البيانات موجودة لكن الإعدادات قد تحتاج تحديث'
                        });
                    }
                } catch (e) {
                    tests.push({
                        name: 'إنشاء قاعدة البيانات',
                        status: 'error',
                        message: 'خطأ: ' + e.message
                    });
                }
                
                // Test 3: CRUD Operations
                try {
                    const testCustomer = {
                        name: 'عميل تجريبي',
                        phone: '01234567890',
                        email: '<EMAIL>'
                    };
                    
                    const created = window.dbManager.create('customers', testCustomer);
                    const read = window.dbManager.read('customers', created.id);
                    const updated = window.dbManager.update('customers', created.id, { name: 'عميل محدث' });
                    const deleted = window.dbManager.delete('customers', created.id);
                    
                    if (created && read && updated && deleted) {
                        tests.push({
                            name: 'عمليات CRUD',
                            status: 'success',
                            message: 'جميع عمليات الإنشاء والقراءة والتحديث والحذف تعمل بنجاح'
                        });
                    } else {
                        tests.push({
                            name: 'عمليات CRUD',
                            status: 'error',
                            message: 'فشل في بعض عمليات CRUD'
                        });
                    }
                } catch (e) {
                    tests.push({
                        name: 'عمليات CRUD',
                        status: 'error',
                        message: 'خطأ: ' + e.message
                    });
                }
                
                // Test 4: Sample Data Function
                try {
                    if (typeof window.loadSampleData === 'function') {
                        tests.push({
                            name: 'البيانات التجريبية',
                            status: 'success',
                            message: 'دالة تحميل البيانات التجريبية متاحة'
                        });
                    } else {
                        tests.push({
                            name: 'البيانات التجريبية',
                            status: 'error',
                            message: 'دالة تحميل البيانات التجريبية غير متاحة'
                        });
                    }
                } catch (e) {
                    tests.push({
                        name: 'البيانات التجريبية',
                        status: 'error',
                        message: 'خطأ: ' + e.message
                    });
                }
                
                // Test 5: LocalStorage
                try {
                    localStorage.setItem('hawsaba_test', 'test');
                    const testValue = localStorage.getItem('hawsaba_test');
                    localStorage.removeItem('hawsaba_test');
                    
                    if (testValue === 'test') {
                        tests.push({
                            name: 'التخزين المحلي',
                            status: 'success',
                            message: 'التخزين المحلي يعمل بشكل صحيح'
                        });
                    } else {
                        tests.push({
                            name: 'التخزين المحلي',
                            status: 'error',
                            message: 'مشكلة في التخزين المحلي'
                        });
                    }
                } catch (e) {
                    tests.push({
                        name: 'التخزين المحلي',
                        status: 'error',
                        message: 'خطأ: ' + e.message
                    });
                }
                
                // Display results
                displayTestResults(tests);
            }, 1000);
        }
        
        function displayTestResults(tests) {
            const resultsDiv = document.getElementById('test-results');
            let html = '';
            
            const successCount = tests.filter(t => t.status === 'success').length;
            const errorCount = tests.filter(t => t.status === 'error').length;
            const warningCount = tests.filter(t => t.status === 'warning').length;
            
            html += `
                <div class="alert alert-info">
                    <h5><i class="fas fa-chart-bar me-2"></i>نتائج الاختبار</h5>
                    <p class="mb-0">
                        <span class="badge bg-success me-2">${successCount} نجح</span>
                        <span class="badge bg-warning me-2">${warningCount} تحذير</span>
                        <span class="badge bg-danger">${errorCount} فشل</span>
                    </p>
                </div>
            `;
            
            tests.forEach(test => {
                const cardClass = test.status === 'success' ? 'test-success' : 
                                 test.status === 'warning' ? 'test-warning' : 'test-error';
                const iconClass = test.status === 'success' ? 'fa-check-circle text-success' : 
                                 test.status === 'warning' ? 'fa-exclamation-triangle text-warning' : 'fa-times-circle text-danger';
                
                html += `
                    <div class="card test-card ${cardClass}">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas ${iconClass} me-2"></i>
                                ${test.name}
                            </h6>
                            <p class="card-text mb-0">${test.message}</p>
                        </div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
